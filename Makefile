MODULE := zzz-support-platform
PLATFORM = $(platform)
ifeq ("$(PLATFORM)", "")
	PLATFORM := "web"
endif
DOCKERVER := v1.0.0

docker:
	echo "build $(MODULE) docker images"
	docker login --username=mihoyo_nap --password=swF3kJdQda156kwlHH5M registery-nap-registry.cn-shanghai.cr.aliyuncs.com
	docker build -t registery-nap-registry.cn-shanghai.cr.aliyuncs.com/nap-ops/$(MODULE)-${PLATFORM}:$(DOCKERVER) .

docker-push:
	echo "push $(MODULE) docker images"
	docker login --username=mihoyo_nap --password=swF3kJdQda156kwlHH5M registery-nap-registry.cn-shanghai.cr.aliyuncs.com
	docker push registery-nap-registry.cn-shanghai.cr.aliyuncs.com/nap-ops/$(MODULE)-$(PLATFORM):$(DOCKERVER)
