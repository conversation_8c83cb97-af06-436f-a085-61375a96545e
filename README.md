# 绝区零自助平台

提供一些共用的服务，如包括日志、私服管理、轻应用等等，开放给运维以外其他用户使用

# 图片库参考
https://www.iconfont.cn/

# 项目参考
参考布局 [pro-layout](https://github.com/sendya/preview-pro)

项目使用了[vue3](https://cn.vuejs.org/guide/introduction.html) + [Ant Design vue](https://antdv.com/docs/vue/introduce-cn)开发

Ant组件可以参考：[官方文档](https://antdv.com/components/overview-cn/)

pro-components组件布局示例参考：[pro-layout](https://github.com/vueComponent/pro-components/tree/next/packages/pro-layout/examples)

ant-design-vue可参考：[Github地址](https://github.com/vueComponent/ant-design-vue)

开箱即用的中台前端/设计解决方案参考：[ANTD PRO VUE](https://github.com/vueComponent/ant-design-vue-pro)

# Start
```shell
# 如果你的网络环境不佳，推荐使用 cnpm
npm install

npm run dev

# 生产环境，可生成dist目录，编译后的静态页面
npm run build
```

# 部署
拉取当前项目的代码,执行docker-compose up -d

## Ant Design Vue Pro Layout

This template should help get you started developing with Vue 3 and Ant Design Vue 2 in Vite.

### Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=johnsoncodehk.volar)

### Type Support For `.vue` Imports in TS

Since TypeScript cannot handle type information for `.vue` imports, they are shimmed to be a generic Vue component type by default. In most cases this is fine if you don't really care about component prop types outside of templates. However, if you wish to get actual prop types in `.vue` imports (for example to get props validation when using manual `h(...)` calls), you can enable Volar's `.vue` type support plugin by running `Volar: Switch TS Plugin on/off` from VSCode command palette.
