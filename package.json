{"name": "preview-pro", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "dev:force": "vite --force", "build": "vue-tsc --noEmit && vite build", "build:ghpages": "vue-tsc --noEmit && vite build --base=/preview-pro/ --mode=ghpages", "report": "vite build", "preview": "vite preview --port 5050", "test:unit": "vitest --environment jsdom", "typecheck": "vue-tsc --noEmit && vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@ant-design-vue/pro-layout": "^3.2.5", "@ant-design/icons-vue": "^6.1.0", "@antv/g2": "^5.2.7", "@antv/g6": "^4.8.15", "@codemirror/lang-yaml": "^6.1.1", "@microsoft/fetch-event-source": "^2.0.1", "ant-design-vue": "^4.2.3", "axios": "^1.3.4", "codemirror": "^6.0.1", "dayjs": "^1.11.7", "md5": "^2.3.0", "monaco-editor": "^0.39.0", "monaco-editor-vue": "^1.0.10", "monaco-editor-webpack-plugin": "^7.1.0", "pinia": "^2.0.33", "vue": "^3.2.33", "vue-codemirror": "^6.1.1", "vue-request": "^2.0.3", "vue-router": "^4.0.14", "vxe-table": "^4.6.25"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.3", "@types/node": "^17.0.25", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vue/tsconfig": "^0.1.3", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.3", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.6.0", "typescript": "~4.5.5", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.22.0", "vite": "^2.9.5", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vitest": "^0.9.3", "vue-tsc": "^0.34.6"}, "vite": {"optimizeDeps": {"include": ["@ant-design-vue/pro-layout", "ant-design-vue/es", "vue", "vue-router"]}}}