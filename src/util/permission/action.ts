import { useCurrentSFStateWithout } from '@/stores/modules/sf';

export function isActionDisabled(action: string): boolean {
  const sfState = useCurrentSFStateWithout();
  const currentSFActions = sfState.getCurrentSFActions;
  // console.log(currentSFActions, `region:${action}`, !currentSFActions[`region:${action}`]);
  return !currentSFActions[`region:${action}`];
}

export function isLocked() {
  const sfState = useCurrentSFStateWithout();
  const currentSFStatus = sfState.getCurrentSFStatus;
  return currentSFStatus.Locked;
}
