import { message } from 'ant-design-vue';
// import { useRoute, useRouter } from 'vue-router';
// const route = useRoute(); // 获取当前路由
// const router = useRouter(); //获取项目中使用的router对象
import { useUserStoreWithOut } from '@/stores/modules/user';

message.config({
  duration: 3,
  maxCount: 2,
});

const reloadWithoutQueryParams = () => {
  // 获取当前 URL
  const currentUrl = window.location.href;

  // 创建一个 URL 对象
  const url = new URL(currentUrl);

  // 移除查询参数
  url.search = '';

  // 重新加载页面
  window.location.href = url.toString();
};

export const showMessage = (
  status: number | string,
  code?: number | string,
  msg?: string,
): string => {
  let content = '';
  const userStore = useUserStoreWithOut();

  // console.log('showMessage: ', status, code, msg);

  switch (status) {
    case 400:
      content = '请求错误(400)';
      break;
    case 401:
      content = '未授权，请重新登录(401)';
      userStore.loginOut();
      reloadWithoutQueryParams();
      break;
    case 403:
      content = '拒绝访问(403)';
      break;
    case 404:
      content = '请求出错(404)';
      break;
    case 408:
      content = '请求超时(408)';
      break;
    case 500:
      content = '服务器错误(500)';
      break;
    case 501:
      content = '服务未实现(501)';
      break;
    case 502:
      content = '网络错误(502)';
      break;
    case 503:
      content = '服务不可用(503)';
      break;
    case 504:
      content = '网络超时(504)';
      break;
    case 505:
      content = 'HTTP版本不受支持(505)';
      break;
    default:
      content = `连接出错(${status})!`;
  }
  if (msg) {
    content = `${content}，${msg}`;
  } else {
    content = `${content}，请检查网络或联系管理员！`;
  }
  if (0 === code) {
    if (msg) {
      message.success(msg);
    }
  } else {
    message.error(content);
  }
  // return content;
};
