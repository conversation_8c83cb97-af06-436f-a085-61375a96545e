import axios from 'axios';
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
} from 'axios';
import { showMessage } from './status';
import type { IResponse } from './type';
import { useUserStore } from '@/stores/modules/user';

const service: AxiosInstance = axios.create({
  // baseURL: import.meta.env.VITE_APP_API_BASEURL,
  timeout: 20000,
});

// axios实例拦截请求
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStore();
    const token = userStore.getToken;
    if (token) {
      config.headers.Authorization = token;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  },
);

// axios实例拦截响应
service.interceptors.response.use(
  (response: AxiosResponse) => {
    if (response.status === 200) {
      return response;
    }
    showMessage(response.status);
    return response;
  },
  // 请求失败
  (error: any) => {
    const { response } = error;
    if (response) {
      // 请求已发出，但是不在2xx的范围
      showMessage(
        response.status,
        response.code,
        response.data.message ? response.data.message : response.data.msg,
      );
      return Promise.reject(response.data);
    }
    showMessage('网络连接异常,请稍后再试!');
  },
);

const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
  const conf = config;
  return new Promise((resolve, reject) => {
    service
      .request<any, AxiosResponse<IResponse>>(conf)
      .then((res: AxiosResponse<IResponse>) => {
        // console.log('res: ', res.data);
        const { data, code, msg } = res.data;
        // console.log('res: ', data, code, msg);
        if (data != null) {
          resolve(data as T);
        } else {
          // console.log('showMessage: ', res.status, code, msg, res.data.message);

          showMessage(res.status, code, res.data.message ? res.data.message : res.data.msg);
        }
        resolve(data);
      })
      .catch(error => {
        reject(error);
      });
  });
};

export function get<T = any>(config: AxiosRequestConfig): Promise<T> {
  return request({ ...config, method: 'GET' });
}

export function post<T = any>(config: AxiosRequestConfig): Promise<T> {
  return request({ ...config, method: 'POST' });
}

export function del<T = any>(config: AxiosRequestConfig): Promise<T> {
  return request({ ...config, method: 'DELETE' });
}

export function put<T = any>(config: AxiosRequestConfig): Promise<T> {
  return request({ ...config, method: 'PUT' });
}

export function patch<T = any>(config: AxiosRequestConfig): Promise<T> {
  return request({ ...config, method: 'PATCH' });
}

export function raw<T = any>(config: AxiosRequestConfig): Promise<T> {
  return service.request(config);
}

export default request;

export type { AxiosInstance, AxiosResponse };
