import { Modal } from 'ant-design-vue';
import type { RegionStatus } from '@/api/model';

// 例如，在某个方法中调用确认框：
// 封装确认框，返回 Promise<boolean>
export function showConfirm(content: string): Promise<boolean> {
  return new Promise(resolve => {
    Modal.confirm({
      title: '镜像服确认操作',
      content: content,
      okText: '确认',
      cancelText: '取消',
      width: '35%',
      onOk() {
        console.log('点击了确认');
        resolve(true); // 用户确认后 resolve(true)
      },
      onCancel() {
        console.log('点击了取消');
        resolve(false); // 用户取消后 resolve(false)
      },
    });
  });
}

export async function checkMirrorConfig(
  currentSFStatus: RegionStatus,
  permCode: string,
  action_name: string,
) {
  if (currentSFStatus.mirror_config) {
    const mirror_config = JSON.parse(currentSFStatus.mirror_config);
    // 如果 mirror_region 存在且有内容
    if (
      mirror_config.mirror_region &&
      mirror_config.mirror_region.length > 0 &&
      mirror_config.mirror_region.is_enable
    ) {
      // 若需要确认的操作存在于 actions 中，则显示确认框
      if (mirror_config.actions.includes(permCode)) {
        const region_info = mirror_config.mirror_region.join(', ');
        const content = `以下 ${mirror_config.mirror_region.length} 个区服:\n ${region_info} 会被同时进行 ${action_name} 操作，你确定要进行此操作吗？`;
        const confirmed = await showConfirm(content); // 等待用户点击确认或取消
        if (!confirmed) {
          return false;
        }
      }
    }
  }
  return true;
}
