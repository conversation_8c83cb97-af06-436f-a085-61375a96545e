const myLogRouter = {
  path: '/log',
  name: 'log',
  meta: { title: '日志平台', icon: 'icon-dengpaotishi', openKeys: ['/log/server'] },
  redirect: () => ({ name: 'sls' }),
  children: [
    {
      path: 'sls',
      name: 'sls',
      meta: { title: 'SLS日志', key: 'sls', icon: 'icon-jiekoupeizhi' },
      component: () => import('@/views/log/SlsLog.vue'),
    },
    {
      path: 'server',
      name: 'server',
      meta: { title: '服务器日志', key: 'server', icon: 'icon-jiekoupeizhi' },
      children: [
        {
          path: 'view',
          name: 'server_log_view',
          meta: { title: '日志查询', key: 'server_log_view', icon: 'icon-jiekoupeizhi' },
          component: () => import('@/views/log/ServerLog.vue'),
        },
        {
          path: 'history',
          name: 'server_log_history',
          meta: { title: '查询历史', key: 'server_log_history', icon: 'icon-jiekoupeizhi' },
          component: () => import('@/views/log/ServerLogHistory.vue'),
        },
      ],
    },
  ],
};

export default myLogRouter;
