import BlankLayout from '@/layouts/BlankLayout.vue';

const myAppRouter = {
  path: 'myapp',
  name: 'myapp',
  meta: { title: '我的应用', icon: 'icon-fenleiorguangchangorqita' },
  component: BlankLayout,
  redirect: () => ({ name: 'copy-account' }),

  children: [
    // {
    //   path: 'copy-account',
    //   name: 'copy-account',
    //   meta: { title: '账号拷贝', key: 'copy-account', icon: 'icon-shujuku' },
    //   component: () => import('@/views/myapp/copyaccount/Index.vue'),
    // },
    // {
    //   path: 'dispatch',
    //   name: 'dispatch',
    //   meta: { title: 'Dispatch查询', key: 'dispatch', icon: 'icon-shujuku' },
    //   component: () => import('@/views/myapp/dispatch/dispatch_info.vue'),
    // },
    // {
    //   path: 'sql-query',
    //   name: 'sql-query',
    //   meta: { title: '数据库查询', key: 'sql-query', icon: 'icon-shujuku' },
    //   component: () => import('@/views/demo/mydemo.vue'),
    // },
    // {
    //   path: 'pre-ifix',
    //   name: 'pre-ifix',
    //   meta: { title: 'PreIFix发布', key: 'pre_ifix', icon: 'icon-shujuku' },
    //   component: () => import('@/views/myapp/preifix/Index.vue'),
    // },
  ],
};
export default myAppRouter;
