import BlankLayout from '@/layouts/BlankLayout.vue';
import { useUserStoreWithOut } from '@/stores/modules/user';
// 官服区域配置
const officialServers = [
  {
    key: 'prod_gf_cn',
    title: '国服',
    icon: 'icon-china',
  },
  {
    key: 'prod_gf_jp',
    title: '亚服',
    icon: 'icon-japan',
  },
  {
    key: 'prod_gf_us',
    title: '美服',
    icon: 'icon-america',
  },
  {
    key: 'prod_gf_eu',
    title: '欧服',
    icon: 'icon-europe',
  },
  {
    key: 'prod_gf_sg',
    title: '港澳台服',
    icon: 'icon-hong-kong',
  },
];
const userStore = useUserStoreWithOut();
// 构建路由配置
const regionManageRouter = {
  path: '/region-manage',
  name: 'region-manage',
  meta: {
    title: '区服管理',
    icon: 'icon-xiai',
    is_admin: true,
    hideInMenu: !userStore.getUser.is_admin,
  },
  component: BlankLayout,
  redirect: `/region-manage/prod_gf_cn/dashboard`,
  children: officialServers.map(({ key, title, icon }) => ({
    path: key,
    name: key,
    meta: {
      title,
      icon,
      isRegion: true,
    },
    component: () => import('@/views/region-manage/index.vue'),
    redirect: `/region-manage/${key}/dashboard`,
    children: [
      {
        path: 'dashboard',
        name: `${key}-dashboard`,
        component: () => import('@/views/region-manage/dashboard/index.vue'),
        meta: { hideInMenu: true },
      },
      // {
      //   path: 'basic-info',
      //   name: `${key}-basic-info`,
      //   component: () => import('@/views/region-manage/details/basic-info.vue'),
      //   meta: { hideInMenu: true },
      // },
      {
        path: 'activity-info',
        name: `${key}-activity-info`,
        component: () => import('@/views/region-manage/details/activity-info.vue'),
        meta: { hideInMenu: true },
      },
      // {
      //   path: 'version-info',
      //   name: `${key}-version-info`,
      //   component: () => import('@/views/region-manage/details/version-info.vue'),
      //   meta: { hideInMenu: true },
      // },
      // {
      //   path: 'business-log',
      //   name: `${key}-business-log`,
      //   component: () => import('@/views/region-manage/logs/business-log.vue'),
      //   meta: { hideInMenu: true },
      // },
      // {
      //   path: 'load-balance/dp-level-1',
      //   name: `${key}-dp-level-1`,
      //   component: () => import('@/views/region-manage/logs/load-balance/dp-level-1.vue'),
      //   meta: { hideInMenu: true },
      // },
      // {
      //   path: 'load-balance/dp-level-2',
      //   name: `${key}-dp-level-2`,
      //   component: () => import('@/views/region-manage/logs/load-balance/dp-level-2.vue'),
      //   meta: { hideInMenu: true },
      // },
    ],
  })),
};

export default regionManageRouter;
