import { createRouter, createWebHistory } from 'vue-router';
import BasicLayout from '../layouts/BasicLayout.vue';
import WelcomePage from '@/views/demo/Page1.vue';
import myRegionRouter from '@/router/MyRegionRouter';
import mySFRouter from '@/router/MySFRouter';
import permissionRouter from '@/router/PermissionRouter';
import myAppRouter from '@/router/MyAppRouter';
import myLogRouter from '@/router/MyLogRouter';
import regionManageRouter from "@/router/RegionManageRouter";

const baseRoutes = [
  {
    path: '/',
    name: 'index',
    meta: { title: 'Home', key: 'home' },
    component: BasicLayout,
    redirect: '/my-region',
    children: [
      {
        path: 'welcome',
        name: 'welcome',
        meta: { title: '欢迎', icon: 'icon-shangjia' },
        component: WelcomePage,
      },
      myAppRouter,
      permissionRouter,
      myLogRouter,
      // mySFRouter,
      myRegionRouter,
      regionManageRouter,
    ],
  },
  {
    path: '/:path(.*)*',
    component: () => import('@/views/exception/404.vue'),
    hidden: true,
  },
];

export const router = createRouter({
  history: createWebHistory(),
  routes: baseRoutes,
});

// config router
// 配置路由器
export function setupRouter(app: App<Element>) {
  app.use(router);
}
