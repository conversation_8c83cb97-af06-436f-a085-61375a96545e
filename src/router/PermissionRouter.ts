import BlankLayout from '@/layouts/BlankLayout.vue';

const permissionRouter = {
  path: '/perm',
  name: '权限中心',
  meta: { title: '权限中心', icon: 'icon-fenleiorguangchangorqita' },
  component: BlankLayout,
  redirect: () => ({ name: 'my-perm' }),
  children: [
    {
      path: 'my-perm',
      name: 'my-perm',
      meta: { title: '我的权限', key: 'my-perm', icon: 'icon-jiekoupeizhi' },
      component: () => import('@/views/permission/perm_detail.vue'),
    },
    {
      path: 'perm-apply',
      name: 'perm-apply',
      meta: { title: '权限申请', key: 'perm-apply', icon: 'icon-jiekoupeizhi' },
      component: () => import('@/views/permission/perm_apply.vue'),
    },
    {
      path: 'apply-review',
      name: 'apply-review',
      meta: { title: '权限审批', key: 'apply-review', icon: 'icon-jiekoupeizhi' },
      component: () => import('@/views/permission/apply_review.vue'),
    },
  ],
};
export default permissionRouter;
