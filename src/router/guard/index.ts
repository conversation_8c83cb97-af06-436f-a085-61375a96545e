import { Modal, message } from 'ant-design-vue';
import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import type { MenuItem } from '@/api/model';
import { useUserStoreWithOut } from '@/stores/modules/user';
import type {
  LocationQuery,
  Router,
  RouteRecord,
  RouteRecordNormalized,
  RouteRecordRaw,
} from 'vue-router';
import { router } from '@/router';
import { objectType } from 'ant-design-vue/es/_util/type';

const userStore = useUserStoreWithOut();
let isLoadDynamicRouter = false;

export function setupRouterGuard(router: Router) {
  createLoginGuard(router);
  // createPermissionGuard(router);
}

function createLoginGuard(router: Router) {
  const whileList = ['/login', '/api/v1/login', '/logpt/sferror'];

  router.beforeEach(async (to, _, next) => {
    const ticket = to.query.ticketKey;
    // 如果有ticket，则先校验ticket
    if (ticket) {
      const ticketStr = ticket.toString();
      try {
        const user = await userStore.ssologin(ticketStr);
        // 登录成功进入路由首页
        if (user) {
          message.success('登录成功！欢迎回来');
          await setupDynamicRoutes(router); // 必须在登录后，设置一下动态路由，否则首次登录没有路由
          next({ ...to, query: { ...to.query, ticketKey: undefined } });
        }
      } catch (error) {
        // console.log('ticketKey无效, error: ', error);
        next({ ...to, query: { ...to.query, ticketKey: undefined } });
      }
      return;
    }
    const token = localStorage.getItem('token');
    //白名单 有值 或者登陆过存储了token信息可以跳转 否则就去登录页面
    if (whileList.includes(to.path) || token) {
      const redirect = to.query?.redirect;
      // console.log('进入路由守卫....');
      if (redirect) {
        next({ path: redirect.toString() });
      }
      next();
    } else {
      showConfirm(to.fullPath);
    }
  });
}

export async function setupDynamicRoutes(router: Router) {
  const token = localStorage.getItem('token');
  // 判断是否登录，登录后才获取动态路由
  if (token && !isLoadDynamicRouter) {
    try {
      const menus = await userStore.getUserMenu('support');
      // console.log('获取用户权限数据: ', menus);
      if (menus.length > 0) {
        menus[0].children.forEach((route: MenuItem) => {
          // console.log('parentRoute.children.push: ', route);
          if (route.menuName == 'myapp') {
            route.children.forEach((route: MenuItem) => {
              const parentName = 'myapp';
              // console.log('children.component: ', route.component, ' parentName: ', parentName);

              const parentRoute: RouteRecordNormalized | undefined = router
                .getRoutes()
                .find(route => route.name === parentName);

              const childrenRoute = {
                path: route.path,
                name: route.menuName,
                meta: JSON.parse(route.meta),
                component: () => importViewsFile(route.component),
              };
              if (!router.hasRoute(route.code)) {
                parentRoute.children.push(childrenRoute);
                router.addRoute(parentName, childrenRoute);
              }
              // console.log('all route add : ', router.getRoutes());
              userStore.setDynamicRoute(router.getRoutes());
              isLoadDynamicRouter = true;
            });
          } else {
            console.error('非法的菜单code: ', route.code, ' ，至少包含一个冒号(:)');
          }
        });
      }
    } catch (error) {
      console.error('Error fetching dynamic routes:', error);
    }
  }
}

// 动态导入view下所有模块
const allModules = import.meta.glob('@/views/**/*.vue');

function importViewsFile(path): Promise<any> {
  // console.log('import modules: ', allModules);
  if (path.startsWith('/')) {
    path = path.substring(1);
  }
  if (path.startsWith('@/views/')) {
    path = path.replace('@/views/', '');
  }
  let page = '';
  if (path.endsWith('.vue')) {
    page = `../../views/${path}`;
  } else {
    page = `../../views/${path}.vue`;
  }
  return new Promise((resolve, reject) => {
    let flag = true;
    for (const path in allModules) {
      if (path == page) {
        flag = false;
        allModules[path]().then(mod => {
          // console.log(path, mod);
          resolve(mod);
        });
      }
    }
    if (flag) {
      reject('该文件不存在:' + page);
    }
  });
}

const showConfirm = (currentPath: string) => {
  Modal.warn({
    title: '页面授权失效',
    icon: createVNode(ExclamationCircleOutlined),
    content: '即将跳转至sso登录页。',
    okText: '确定',
    centered: true,
    onOk() {
      const baseURL = `${window.location.protocol}//${window.location.host}`;
      const url = new URL(baseURL);
      url.pathname = currentPath;
      const clientID = '02488564150d44c0';
      const redirectURI = encodeURIComponent(url.toString());
      window.location.href = `https://api.openout.mihoyo.com/iam/authentication/out/v1/oauth2/authorize?clientId=${clientID}&redirectUri=${redirectURI}&callbackType=h5`;
    },
  });
};
