import { h } from 'vue';
import {
  ApartmentOutlined,
  BranchesOutlined,
  ContainerOutlined,
  DesktopOutlined,
  ExperimentOutlined,
  FileSearchOutlined,
  FundOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue';

const myRegionRouter = {
  path: 'my-region',
  name: 'my-region',
  meta: { title: '我的区服', icon: 'icon-shangjia' },
  component: () => import('@/views/my-region/index.vue'),
  redirect: () => ({ name: 'region-version' }),
  children: [
    {
      path: 'version',
      name: 'region-version',
      meta: {
        title: '区服版本',
        key: 'region-SFVersion',
        hideInMenu: true,
        icon: () => h(BranchesOutlined),
      },
      component: () => import('@/views/my-region/SFVersion.vue'),
    },
    {
      path: 'ufightserver',
      name: 'region-ufightserver',
      meta: {
        title: '小游戏版本',
        key: 'region-ufightserver',
        hideInMenu: true,
        icon: () => h(ApartmentOutlined),
      },
      component: () => import('@/views/my-region/SFUfightVersion.vue'),
    },
    {
      path: 'gameLog',
      name: 'region-gameLog',
      meta: {
        title: '游戏日志',
        key: 'region-GameLog',
        hideInMenu: true,
        icon: () => h(FileSearchOutlined),
      },
      component: () => import('@/views/my-region/SFGameLog.vue'),
    },
    {
      path: 'taskLog',
      name: 'region-errLog',
      meta: {
        title: '任务错误日志',
        key: 'region-errLog',
        hideInMenu: true,
        icon: () => h(ContainerOutlined),
      },
      component: () => import('@/views/my-region/SFErrorLog.vue'),
    },
    {
      path: 'monitor',
      name: 'region-monitor',
      meta: {
        title: '监控',
        key: 'region-SFMonitor',
        hideInMenu: true,
        icon: () => h(FundOutlined),
      },
      component: () => import('@/views/my-region/SFMonitor.vue'),
    },
    {
      path: 'sfConfig',
      name: 'region-sfConfig',
      meta: {
        title: '区服设置',
        key: 'region-sfConfig',
        hideInMenu: true,
        icon: () => h(SettingOutlined),
      },
      component: () => import('@/views/my-region/SFConfig.vue'),
    },
    {
      path: 'sfLog',
      name: 'region-sfLog',
      meta: {
        title: '高级日志',
        key: 'region-sflog',
        hideInMenu: true,
        icon: () => h(ExperimentOutlined),
      },
      component: () => import('@/views/my-region/SFLog.vue'),
    },
    {
      path: 'sfXml',
      name: 'region-sfXml',
      meta: {
        title: 'XML配置',
        key: 'region-sfXml',
        hideInMenu: true,
        icon: () => h(FileSearchOutlined),
      },
      component: () => import('@/views/my-region/SFXml.vue'),
    },
    {
      path: 'region-detail',
      name: 'region-detail',
      meta: {
        title: '区服详情',
        key: 'region-detail',
        hideInMenu: true,
        icon: () => h(DesktopOutlined),
      },
      component: () => import('@/views/my-region/SFDetail.vue'),
    },
    {
      path: 'region-operate',
      name: 'region-operate',
      meta: {
        title: '操作日志',
        key: 'region-detail',
        hideInMenu: true,
        icon: () => h(DesktopOutlined),
      },
      component: () => import('@/views/my-region/SFOperateLog.vue'),
    },
  ],
};

export default myRegionRouter;
