import BlankLayout from '@/layouts/BlankLayout.vue';

const mySFRouter = {
  path: '/sfmanage',
  name: 'sfmanage',
  meta: { title: '私服管理', icon: 'icon-qingdan' },
  component: BlankLayout,
  redirect: () => ({ name: 'mysf' }),
  children: [
    {
      path: 'report',
      name: 'report',
      meta: { title: '审计数据', key: 'auth', icon: 'icon-shujuku' },
      component: () => import('@/views/demo/mydemo.vue'),
    },
  ],
};

export default mySFRouter;
