import { del, get, post, put, patch } from '@/util/request/request';
import type {
  StartSFReq,
  RegionArgReq,
  ModuleReq,
  BrachInfoRes,
  BranchInfoReq,
  UpdateSfCodeReq,
  UpdateSfDataReq,
  UpdateSfCodeAndDataReq,
  modifyServerTimeOffsetReq,
  ServerLogReq,
  ExecModuleCmdReq,
  BranchReq,
  RegionConfigResp,
  UpdateRegionScheduleConfigReq,
  RegionStatus,
  UpdateSfStatusReq,
  ListReq,
  CopyAccountHistoryResp,
  CopyAccountRegionResp,
  CopyAccountHistoryReq,
  sfActionsRes,
  UpdateResp,
  RepairSFReq,
  ServerLogResp,
  FetchModuleReq,
  DataCheckInfo,
  UpdateSfPatchReq,
  PatchResp,
  BaseParams,
  UpdateXmlConfigReq,
  EnumConfigRes,
  GlobalDispatchInfoRes,
  GlobalDispatchInfoSearchParams,
  RegionDispatchInfoSearchParams,
  RegionDispatchInfoRes,
  BranchInfo,
  ServerTimeResp,
  CopyAccountBlackListReq,
  CopyAccountBlackListResp,
  DeleteCopyAccountBlackList,
  CreateCopyAccountBlackList,
  CopyAccountLogResp,
  TaskLogReq,
  UpdateServerXmlReq,
  GetPreIFixBuildReq,
  GetPreIFixBuildResp,
  GetPreIFixDeployReq,
  GetPreIFixDeployResp,
  GetCurrentPreIFixDeployResp,
  CreatePreIFixDeployReq,
  DeletePreIFixDeployReq,
  GetCurrentPreIFixDeployReq,
  UpdateRegionMirrorConfigReq,
  UpdateRegionConfigReq,
  RegionMirrorConfig,
  GetInnerDevGameResReq,
  GetInnerDevGameResResp,
  GetInnerDevClientDataReq,
  GetInnerDevClientDataResp,
  PostInnerDevGameResReq,
  PostInnerDevClientDataReq,
  PostDrawBoxDownloadReq,
} from './model';

enum URL {
  branchUri = '/api/v1/branch', // 新版本接口 获取所有代码版本信息 与 所有的数值版本信息
  codeBranchInfoUri = '/api/v1/branch/code',
  dataBranchInfoUri = '/api/v1/branch/data',
  //特殊处理 Ufight_server
  ufightCodeBranchInfoUri = '/api/v1/branch/ufight/code',
  ufightDataBranchInfoUri = '/api/v1/branch/ufight/data',

  //---------------------------------------------------  区服分支信息 --------------------------------------------
  currentCodeBranchInfoUri = '/api/v1/region/code/branch',
  currentDataBranchInfoUri = '/api/v1/region/data/branch',

  currentUfightCodeBranchInfoUri = '/api/v1/region/ufight/code/branch',
  ufightCurrentDataBranchInfoUri = '/api/v1/region/ufight/data/branch',

  currentDataBranchCheckInfoUri = '/api/v1/region/data/branch/check',
  //---------------------------------------------------  区服基本信息 --------------------------------------------
  getRegionDetailUri = '/api/v1/region/detail',

  getAllModuleUri = '/api/v1/region/module',
  getSfStatusUri = '/api/v1/region/status',

  // -------------------------------- 更新私服 ---------------------------------------------------

  updateSfUri = '/api/v1/region/code/update',
  getPatchUri = '/api/v1/region/code/patch',
  updateSfPatchUri = '/api/v1/region/code/patch',
  rollbackSfUri = '/api/v1/region/code/rollback', // 回滚代码

  updateSfDataUri = '/api/v1/region/data/update',
  reloadSfDataUri = '/api/v1/region/data/reload', // 重载数值
  // 更新bin与数值
  updateSfBinAndDataUri = '/api/v1/region/update',
  // 调整时间
  serverTimeOffsetUri = '/api/v1/region/time_offset',
  // code xml配置相关
  updateServerXmlUri = '/api/v1/region/xml_config',
  getXmlConfigUri = '/api/v1/region/xml_config',
  updateXmlConfigUri = '/api/v1/region/xml_config',

  // -------------------------------- 操作私服 ---------------------------------------------------
  startSfUri = '/api/v1/region/start',
  stopSfUri = '/api/v1/region/stop',
  repairSfUri = '/api/v1/region/repair',
  killModuleUri = '/api/v1/region/module/kill',

  getRegionConfigUri = '/api/v1/region/config',
  UpdateRegionScheduleConfigUri = '/api/v1/region/config/schedule', // 区服定时更新配置
  UpdateRegionMirrorConfigUri = '/api/v1/region/config/mirror', // 区服镜像配置

  UpdateSfStatusUri = '/api/v1/region/status',
  //---------------------------------
  // 获取私服其他数据
  getServerLogUri = '/api/v1/region/log/server',
  getTaskLogUri = '/api/v1/region/log/task',
  deleteSfUserUri = '/api/v1/region/user', // method delete
  // 获取区服统计操作数据
  updateStatsUri = '/api/v1/region/operate/stats',
  getOperateUri = '/api/v1/region/operate',

  getActivityUri = '/api/v1/region/activity',
  // -------------------------

  fetchServerLogFileUri = '/api/v1/server_log_file',
  clearScannedServerLogUri = '/api/v1/clear_scanned_server_log',

  CopyAccountHistoryUri = '/api/v1/manager/tools/copy_account_history',
  CopyAccountRegionUri = '/api/v1/manager/tools/regions',
  CopyAccountLogUri = '/api/v1/manager/tools/copy_account_log',
  BatchCopyAccountUri = '/api/v1/manager/tools/batch_copy_account',

  sfActionsUri = '/api/v1/sf/:region/actions',

  enumConfigUri = '/api/v1/enums',
  globalDispatchInfoUri = '/api/v1/dispatch/global',
  regionDispatchInfoUri = '/api/v1/dispatch/region',

  getCopyAccountBlackListUri = '/api/v1/manager/tools/copy_account_black_list',
  batchCreateCopyAccountBlackListUri = '/api/v1/manager/tools/batch_copy_account_black_list',
  batchDeleteCopyAccountBlackListUri = '/api/v1/manager/tools/batch_copy_account_black_list',

  preIFixBuildUri = '/api/v1/pre_ifix/build',
  preIFixDeployUri = '/api/v1/pre_ifix/deploy',
  currentPreIFixDeployUri = '/api/v1/pre_ifix/deploy/current',

  innerDevGameResUri = '/api/v1/inner_dev/game_res',
  innerDevClientDataUri = '/api/v1/inner_dev/client_data',

  drawBoxDownloadUri = '/api/v1/draw_box/download',
}

// 获取私服全部可用操作
const getSfActionsApi = async (region: string) =>
  get<sfActionsRes>({ url: URL.sfActionsUri.replace(':region', region) });

//启动私服
const startSfApi = async (data: StartSFReq) => post({ url: URL.startSfUri, data });
// 停止私服
const stopSfApi = async (data: StartSFReq) => post({ url: URL.stopSfUri, data });
// 修复私服
const repairSfApi = async (data: RepairSFReq) => post({ url: URL.repairSfUri, data });

const getRegionDetailApi = async (region_name: string) =>
  get<[]>({ url: URL.getRegionDetailUri, params: { region: region_name } });
//获取私服的所有模块
const getAllModuleApi = async (data: RegionArgReq) =>
  get<[]>({ url: URL.getAllModuleUri, params: data });

const killModuleApi = async (data: ExecModuleCmdReq) =>
  post<ModuleReq>({ url: URL.killModuleUri, data });

// 获取所有代码与数值分支信息
const getBranchVersionApi = async (data: BranchReq) => get({ url: URL.branchUri, params: data });
const createBranchVersionApi = async (data: BranchInfo) => post({ url: URL.branchUri, data });
const updateBranchVersionApi = async (data: BranchInfo) =>
  patch({ url: `${URL.branchUri}/${data.id}`, data });
const deleteBranchVersionApi = async (id: number) => del({ url: `${URL.branchUri}/${id}` });

//通过代码分支名字 获取最新代码分支的的详情
const getCodeBranchInfoApi = async (data: BranchInfoReq) =>
  get<BrachInfoRes[]>({ url: URL.codeBranchInfoUri, params: data });

const getDataBranchInfoApi = async (data: BranchInfoReq) =>
  get<BrachInfoRes[]>({ url: URL.dataBranchInfoUri, params: data });

// 获取当前私服的分支版本,通过私服名字获取
const getCurrentCodeBranchInfoApi = async (data: RegionArgReq) =>
  get<BrachInfoRes[]>({ url: URL.currentCodeBranchInfoUri, params: data });
const getCurrentDataBranchInfoApi = async (data: RegionArgReq) =>
  get<BrachInfoRes[]>({ url: URL.currentDataBranchInfoUri, params: data });
const getCurrentDataBranchCheckInfoApi = async (data: RegionArgReq) =>
  get<DataCheckInfo>({ url: URL.currentDataBranchCheckInfoUri, params: data });

//通过代码分支名字 获取Ufight_server最新代码分支的的详情
const getUfightCodeBranchInfoApi = async (data: BranchInfoReq) =>
  get<BrachInfoRes[]>({ url: URL.ufightCodeBranchInfoUri, params: data });
const getCurrentUfightCodeBranchInfoApi = async (data: RegionArgReq) =>
  get<BrachInfoRes[]>({ url: URL.currentUfightCodeBranchInfoUri, params: data });
const getUfigntDataBranchInfoApi = async (data: BranchInfoReq) =>
  get<BrachInfoRes[]>({ url: URL.ufightDataBranchInfoUri, params: data });
const getCurrentUfightDataBranchInfoApi = async (data: RegionArgReq) =>
  get<BrachInfoRes[]>({ url: URL.ufightCurrentDataBranchInfoUri, params: data });

// 更新私服
const updateSfApi = async (data: UpdateSfCodeReq) => post({ url: URL.updateSfUri, data });

const rollbackSfApi = async (data: UpdateSfCodeReq) => post({ url: URL.rollbackSfUri, data }); // 回滚私服

const updateSfDataApi = async (data: UpdateSfDataReq) => post({ url: URL.updateSfDataUri, data });

const updateSfBinAndDataApi = async (data: UpdateSfCodeAndDataReq) =>
  post({ url: URL.updateSfBinAndDataUri, data });

const updateServerXmlApi = async (data: UpdateServerXmlReq) =>
  post({ url: URL.updateServerXmlUri, data });

const updateSfPatchApi = async (data: UpdateSfPatchReq) =>
  post({ url: URL.updateSfPatchUri, data });

const reloadSfDataApi = async (data: UpdateSfDataReq) => post({ url: URL.reloadSfDataUri, data });

const modifyServerTimeOffsetApi = async (data: modifyServerTimeOffsetReq) =>
  post({ url: URL.serverTimeOffsetUri, data });
const getServerTimeOffsetApi = async (data: RegionArgReq) =>
  get<ServerTimeResp>({ url: URL.serverTimeOffsetUri, params: data });

//清空私服数据
const deleteSfUserApi = async (data: RegionArgReq) => del({ url: URL.deleteSfUserUri, data });

// 获取私服数据
const getTaskLogApi = async (data: TaskLogReq) => get({ url: URL.getTaskLogUri, params: data });

const getServerLogApi = async (data: ServerLogReq) =>
  get<ServerLogResp>({ url: URL.getServerLogUri, params: data });

const getSfStatusApi = async (data: RegionArgReq) =>
  get<RegionStatus>({ url: URL.getSfStatusUri, params: data });

const fetchServerLogFileApi = async (data: FetchModuleReq) =>
  post({ url: URL.fetchServerLogFileUri, data });

const clearScannedServerLogApi = async (data: RegionArgReq) =>
  post({ url: URL.clearScannedServerLogUri, data });

const getRegionConfigApi = async (data: RegionArgReq) =>
  get<RegionConfigResp>({ url: URL.getRegionConfigUri, params: data });

const updateRegionScheduleConfigApi = async (data: UpdateRegionScheduleConfigReq) =>
  post({ url: URL.UpdateRegionScheduleConfigUri, data });

const updateRegionMirrorConfigApi = async (data: RegionMirrorConfig) =>
  post({ url: URL.UpdateRegionMirrorConfigUri, data });

const updateSfStatusApi = async (data: UpdateSfStatusReq) =>
  post({ url: URL.UpdateSfStatusUri, data });

const getCopyAccountHistoryApi = async (data: ListReq) =>
  get<CopyAccountHistoryResp>({ url: URL.CopyAccountHistoryUri, params: data });

const getCopyAccountRegionApi = async () =>
  get<CopyAccountRegionResp>({ url: URL.CopyAccountRegionUri });

const createCopyAccountHistoryApi = async (data: CopyAccountHistoryReq) =>
  post<string>({ url: URL.CopyAccountHistoryUri, data });

const getCopyAccountLogApi = async (region: string, taskId: string) =>
  get<CopyAccountLogResp>({ url: URL.CopyAccountLogUri, params: { region, task_id: taskId } });

const batchCopyAccountHistoryApi = async (data: BaseParams) =>
  post<string>({ url: URL.BatchCopyAccountUri, data });

//获取私服更新数据
const getUpdateStatsApi = async (region: string) =>
  get<UpdateResp>({
    url: URL.updateStatsUri,
    params: { region: region },
  });

const getOperateLogApi = async params =>
  get<UpdateResp>({
    url: URL.getOperateUri,
    params: params,
  });

const getAllPatchApi = async (region: string) =>
  get<PatchResp[]>({ url: URL.getPatchUri, params: { region: region } });

const getXmlConfigApi = async (region: string) =>
  get<string>({ url: URL.getXmlConfigUri, params: { region: region } });

const updateXmlConfigApi = async (data: UpdateXmlConfigReq) =>
  put<string>({ url: URL.updateXmlConfigUri, data });

const getEnumConfigsApi = async (category: string) =>
  get<EnumConfigRes>({ url: URL.enumConfigUri, params: { category } });

const getGlobalDispatchApi = async (data: GlobalDispatchInfoSearchParams) =>
  post<GlobalDispatchInfoRes>({ url: URL.globalDispatchInfoUri, data });

const getRegionDispatchApi = async (data: RegionDispatchInfoSearchParams) =>
  post<RegionDispatchInfoRes>({ url: URL.regionDispatchInfoUri, data });

const getCopyAccountBlackListApi = async (data: CopyAccountBlackListReq) =>
  get<CopyAccountBlackListResp>({ url: URL.getCopyAccountBlackListUri, params: data });

const batchCreateCopyAccountBlackListApi = async (data: CreateCopyAccountBlackList[]) =>
  post<string>({ url: URL.batchCreateCopyAccountBlackListUri, data });

const batchDeleteCopyAccountBlackListApi = async (data: DeleteCopyAccountBlackList[]) =>
  del<string>({ url: URL.batchDeleteCopyAccountBlackListUri, data });

const getPreIFixBuildApi = async (data: GetPreIFixBuildReq) =>
  get<GetPreIFixBuildResp>({ url: URL.preIFixBuildUri, params: data });

const getPreIFixDeployApi = async (data: GetPreIFixDeployReq) =>
  get<GetPreIFixDeployResp>({ url: URL.preIFixDeployUri, params: data });

const getCurrentPreIFixDeployApi = async (data: GetCurrentPreIFixDeployReq) =>
  get<GetCurrentPreIFixDeployResp>({ url: URL.currentPreIFixDeployUri, params: data });

const createPreIFixDeployApi = async (data: CreatePreIFixDeployReq) =>
  post<string>({ url: URL.preIFixDeployUri, data });

const deletePreIFixDeployApi = async (data: DeletePreIFixDeployReq[]) =>
  del<string>({ url: URL.preIFixDeployUri, data });

const getInnerDevGameResApi = async (data: GetInnerDevGameResReq) =>
  get<GetInnerDevGameResResp>({ url: URL.innerDevGameResUri, params: data });

const getInnerDevClientDataApi = async (data: GetInnerDevClientDataReq) =>
  get<GetInnerDevClientDataResp>({ url: URL.innerDevClientDataUri, params: data });

const postInnerDevGameResApi = async (data: PostInnerDevGameResReq) =>
  post<string>({ url: URL.innerDevGameResUri, data });

const postInnerDevClientDataApi = async (data: PostInnerDevClientDataReq) =>
  post<string>({ url: URL.innerDevClientDataUri, data });
//获取区服活动数据
const getRegionActivityApi = async (region: string) =>
  get<UpdateResp>({
    url: URL.getActivityUri,
    params: { region: region },
  });

const postCollectDrawBoxDownloadApi = async (data: PostDrawBoxDownloadReq) =>
  post<string>({ url: URL.drawBoxDownloadUri, data });

export {
  startSfApi,
  stopSfApi,
  repairSfApi,
  getRegionDetailApi,
  getAllModuleApi,
  killModuleApi,
  getCodeBranchInfoApi,
  getBranchVersionApi, // 新接口 从数据库中获取代码或数值信息
  getCurrentCodeBranchInfoApi,
  getDataBranchInfoApi,
  getCurrentDataBranchInfoApi,
  createBranchVersionApi,
  updateBranchVersionApi,
  deleteBranchVersionApi,
  updateSfApi,
  rollbackSfApi,
  updateSfDataApi,
  updateSfBinAndDataApi,
  reloadSfDataApi,
  modifyServerTimeOffsetApi,
  getServerTimeOffsetApi,
  deleteSfUserApi,
  getServerLogApi,
  clearScannedServerLogApi,
  fetchServerLogFileApi,
  getSfStatusApi,
  getTaskLogApi,
  getUfightCodeBranchInfoApi,
  getCurrentUfightCodeBranchInfoApi,
  getUfigntDataBranchInfoApi,
  getCurrentUfightDataBranchInfoApi,
  getRegionConfigApi,
  updateRegionScheduleConfigApi,
  updateRegionMirrorConfigApi,
  updateSfStatusApi,
  getCopyAccountHistoryApi,
  getCopyAccountRegionApi,
  createCopyAccountHistoryApi,
  getCopyAccountLogApi,
  batchCopyAccountHistoryApi,
  getSfActionsApi,
  getUpdateStatsApi,
  getOperateLogApi,
  getCurrentDataBranchCheckInfoApi,
  getAllPatchApi,
  updateSfPatchApi,
  getXmlConfigApi,
  updateXmlConfigApi,
  getEnumConfigsApi,
  getGlobalDispatchApi,
  getRegionDispatchApi,
  getCopyAccountBlackListApi,
  batchCreateCopyAccountBlackListApi,
  batchDeleteCopyAccountBlackListApi,
  updateServerXmlApi,
  getPreIFixBuildApi,
  getPreIFixDeployApi,
  getCurrentPreIFixDeployApi,
  createPreIFixDeployApi,
  deletePreIFixDeployApi,
  getInnerDevGameResApi,
  getInnerDevClientDataApi,
  postInnerDevGameResApi,
  postInnerDevClientDataApi,
  getRegionActivityApi,
  postCollectDrawBoxDownloadApi,
};
