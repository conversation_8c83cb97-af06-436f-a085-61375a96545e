import { del, get, post } from '@/util/request/request';

enum URL {
  Login = '/api/v1/login',
  Logout = '/api/v1/logout',
  GetUserInfo = '/api/v1/user',
}

/**
 * @description: Login interface parameters
 */
export interface LoginReq {
  username: string;
  password: string;
}

export interface SSOLoginReq {
  ticket: string;
}

export interface LoginRes {
  code: string;
  expire: string;
  msg: string;
  token: string;
}

export interface UserInfoRes {
  id: number;
  user_name: string;
  display_name: string;
  mail: string;
  user_id: string;
  thumbnail: string;
  org_path: string;
  is_admin: boolean;
}

export interface UnBindUserRegionReq {
  region_name: string;
}

const loginApi = async (data: LoginReq) => post<LoginRes>({ url: URL.Login, data });

const ssoLoginApi = async (data: SSOLoginReq) => post<LoginRes>({ url: URL.Login, data });

const getUserInfoApi = async () => get<UserInfoRes>({ url: URL.GetUserInfo });

const unbindUserRegionApi = async (data: UnBindUserRegionReq, id: number) =>
  del({ url: `/api/v1/user/policy`, data });

export { loginApi, getUserInfoApi, ssoLoginApi, unbindUserRegionApi };
