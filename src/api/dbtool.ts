import { del, get, post, put, patch } from '@/util/request/request';
import type { BaseResponse } from '@/api/model';

enum URL {
  regionDetail = '/backend/dbtool/api/v1/query/query_region_list_by_name',
  mysqlDetail = '/backend/dbtool/api/v1/query/query_mysql_list_by_region',
  redisDetail = '/backend/dbtool/api/v1/query/query_redis_list_by_region',
  serverDetail = '/backend/dbtool/api/v1/query/query_server_list_by_region',
}

const getRegionInfoApi = async (region: string) =>
  get<BaseResponse>({
    url: URL.regionDetail,
    params: { region: region, page: 1, page_size: 100 },
  });

const getMysqlDetailApi = async (region: string) =>
  get<BaseResponse>({
    url: URL.mysqlDetail,
    params: { region: region, page: 1, page_size: 100 },
  });

const getRedisDetailApi = async (region: string) =>
  get<BaseResponse>({
    url: URL.redisDetail,
    params: { region: region, page: 1, page_size: 100 },
  });

const getServerDetailApi = async (region: string) =>
  get<BaseResponse>({
    url: URL.serverDetail,
    params: { region: region, page: 1, page_size: 100 },
  });
// 查看区服配置，如停服时间、seed等等
const getRegionConfigDetailApi = async (region: string) =>
  get<BaseResponse>({
    url: '/backend/dbtool/api/v1/query/query_gf_stop_time',
    params: { region: region, page: 1, page_size: 100 },
  });

// 查看官服跑马灯配置
const getRegionAnnounceApi = async (region: string) => {
  // 示例：打印请求参数
  console.log('请求 region:', region);
  // 示例：校验 region 参数
  if (!region) {
    throw new Error('region 参数不能为空');
  }
  // 处理uri
  const suffix = region.replace('prod_', '');

  // 示例：组装参数
  const requestParams = {
    region,
    page: 1,
    page_size: 100,
  };

  // 实际请求
  return get<BaseResponse>({
    url: `/backend/dbtool/api/v1/query/query_${suffix}_announce`,
    params: requestParams,
  });
};
// 查看cn官服登录奖励配置
const getRegionLoginRewardApi = async (region: string) => {
  // 示例：打印请求参数
  console.log('请求 region:', region);
  // 示例：校验 region 参数
  if (!region) {
    throw new Error('region 参数不能为空');
  }
  // 处理uri
  const suffix = region.replace('prod_', '');

  // 示例：组装参数
  const requestParams = {
    region,
    page: 1,
    page_size: 100,
  };

  // 实际请求
  return get<BaseResponse>({
    url: `/backend/dbtool/api/v1/query/query_${suffix}_login_reward`,
    params: requestParams,
  });
};

export {
  getRegionInfoApi,
  getMysqlDetailApi,
  getRedisDetailApi,
  getServerDetailApi,
  getRegionConfigDetailApi,
  getRegionAnnounceApi,
  getRegionLoginRewardApi,
};
