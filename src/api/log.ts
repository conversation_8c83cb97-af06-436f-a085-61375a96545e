import { get, post, raw } from '@/util/request/request';
import type {
  CollectLogRes,
  CollectRegionLogReq,
  DownloadRegionLogReq,
  getRegionSlsInfoReq,
  getRegionSlsInfoRes,
  LogContextReq,
  LogRegionsRes,
  LogSubtasksResp,
  LogTaskHistoryReq,
  LogTaskHistoryResp,
  RegionTaskLogReq,
  RegionTaskLogResp,
  TaskDetailRes,
} from '@/api/model';

enum URL {
  // 获取基本信息
  getLogRegionsUri = '/api/v1/log-svc/regions',
  getRegionSlsInfoUri = '/api/v1/log-svc/sls/info',
  getRegionTaskLogUri = '/api/v1/log-svc/region/task/log',
  collectTaskUri = '/api/v1/log-svc/region/collect',
}

const getLogRegionsApi = async () => get<LogRegionsRes>({ url: URL.getLogRegionsUri });
const getRegionSlsInfoApi = async (data: getRegionSlsInfoReq) =>
  post<getRegionSlsInfoRes>({ url: URL.getRegionSlsInfoUri, data });

const getRegionTaskLogApi = async (data: RegionTaskLogReq) =>
  post<RegionTaskLogResp>({ url: URL.getRegionTaskLogUri, data });

const collectTaskApi = async (data: CollectRegionLogReq) =>
  post<CollectLogRes>({ url: URL.collectTaskUri, data });

const getTaskDetailApi = async (region: string, task_id: string) =>
  get<TaskDetailRes>({ url: `/api/v1/log-svc/region/${region}/task/${task_id}` });

const cancelTaskApi = async (region: string, task_id: string) =>
  get({ url: `/api/v1/log-svc/region/${region}/task/${task_id}/cancel` });

const getLogContextApi = async (data: LogContextReq) =>
  post<RegionTaskLogResp>({ url: '/api/v1/log-svc/region/context', data });

const getLogTaskHistoryApi = async (data: LogTaskHistoryReq) =>
  post<LogTaskHistoryResp>({ url: '/api/v1/log-svc/region/task/history', data });

const getTaskSubtasksApi = async (region: string, task_id: string) =>
  get<LogSubtasksResp>({ url: `/api/v1/log-svc/region/${region}/task/${task_id}/subtasks` });

const downloadTaskLogApi = async (data: DownloadRegionLogReq) =>
  raw({ url: `/api/v1/log-svc/region/task/log/download`, data, method: 'POST' });

export {
  getLogRegionsApi,
  getRegionSlsInfoApi,
  getRegionTaskLogApi,
  collectTaskApi,
  getTaskDetailApi,
  cancelTaskApi,
  getLogContextApi,
  getLogTaskHistoryApi,
  getTaskSubtasksApi,
  downloadTaskLogApi,
};
