import type { Dayjs } from 'dayjs';

export interface BaseParams {
  page?: number;
  size?: number;

  [field: string]: never;
}

export interface Params {
  [key: string]: string | number | boolean; // 允许不同类型的值
}

export interface BaseResponse {
  columns?: string[];
  items?: Record<string, any>[];
  total?: number;

  [field: string]: never;
}

export interface SfDataRes {
  name: string[];
}

export interface StartSFReq {
  account?: string;
  region: string;
}

export interface RepairSFReq {
  account: string;
  region: string;
  repair_list: string[];
  disk_clean_type: string;
}

export interface ModuleRes {
  names: string[];
}

export interface RegionArgReq {
  account?: string;
  region: string;
  uid?: string;
  flush_redis?: boolean;
}

export interface ModuleReq {
  account?: string;
  region: string;
  module_name: string;
}

export interface FetchModuleReq {
  account?: string;
  region: string | null;
  module_name: string;
  fetch_start_time?: string | null;
}

export interface ExecModuleCmdReq {
  account?: string;
  region: string;
  module_name: string;
  command?: string;
}

export interface BrachInfoRes {
  key: string;
  index?: string;
  value: string;
}

export interface DataCheckInfo {
  status: string;
  err_msg: string;
}

export interface BranchInfoReq {
  branch: string;
  region: string;
}

export interface UpdateSfCodeReq {
  account: string;
  region: string;
  branch: string;
  module_name?: string;
}

export interface UpdateSfDataReq {
  account: string;
  region: string;
  data_branch?: string;

  module_name?: string;
}

export interface UpdateSfCodeAndDataReq {
  account: string;
  region: string;
  branch: string;
  data_branch: string;
}

export interface UpdateServerXmlReq {
  region: string;
  branch: string;
}

export interface UpdateSfPatchReq {
  account: string;
  region: string;
  patch_id: string;
}

export interface modifyServerTimeOffsetReq {
  account: string;
  region: string;
  operation: string;
  time: string;
}

export interface ServerLogReq {
  region: string;
  log_type: string;
  search?: string;
  module_name: string;
  page: number;
  size: number;
  start_time?: number;
  end_time?: number;
  log_file?: string;
}

export interface ServerLogResp {
  items: ServerLogRes[];
  total: number;
}

export interface ServerErrorLogResp {
  items: string[];
  total: number;
}

export interface ServerLogRes {
  FILE_NAME?: string;
  FUNC_NAME?: string;
  LINE_NUMBER?: string;
  LOG_LEVEL?: string;
  MSG: string;
  THREAD_ID?: string;
  time?: string;
  TIME?: string;
  CONTENT?: string;
  __source__?: string;
  __tag__?: {
    __hostname__?: string;
    __pack_id__?: string;
    __path__?: string;
    __receive_time__?: string;
    __user_defined_id__?: string;
  };
  __time__?: string;
  __topic__?: string;
}

export interface TaskErrorLogRes {
  agentId: string;
  auto_close: string;
  delta: string;
  exitCode: string;
  explain: string;
  runTime: string;
  startTime: string;
  status: string;
  taskId: string;
  task_type: string;
}

export interface RegionStatus {
  id?: number;
  region_name: string;
  status: number;
  created_time?: string;
  updated_time?: string;
  locked?: boolean;
  Locked: boolean;
  locker?: string;
  lock_duration?: number;
  describe?: string;
  healthy_status: number;
  user_count: number;
  mirror_config: string;
}

export interface GameServerLogReq {
  task_id: string;
  agent_id: string;
}

export interface RegionReq {
  name: string;
}

export interface AuthRegionReq {
  region_names: string[];
}

export interface RegionRes {
  id: number;
  name: string;
  environment: string;
  description: string;
  op_server: string;
  branch: string;
  robot: string;
  used: number;
  mirror_region: string;
  data_branch: string;
}

export interface BranchReq {
  type: string; // code or data
  module_name: string; // all or ufightserver
}

export interface RegionConfigResp {
  id: number;
  region: string;
  code_branch: string;
  data_branch: string;
  update_time: string;
  status: string;
  enable: boolean;
  cron: string;
  next_run_time: string | null;
  last_run_time: string | null;
  account: string;
  timer_type: string;
  enable_at: boolean;
}

export interface UpdateRegionScheduleConfigReq {
  region: string;
  code_branch: string;
  data_branch: string;
  enable: boolean;
  enable_at: boolean;
  account?: string;
  cron: string;
  timer_type: string;
}

export interface RegionMirrorConfig {
  region: string;
  mirror_region: string[];
  actions: string[];
  is_enable: boolean;
  updated_at?: string;
  updated_by?: string;
}

export interface UpdateSfStatusReq {
  region: string;
  locked: boolean;
  describe?: string;
}

export interface ListReq {
  page: number;
  page_size: number;

  [key: string]: any;
}

export interface CopyAccountHistory {
  id: number;
  src_region: string;
  src_env: string;
  src_account_type: string;
  src_account_channel: string;
  src_account_id: string;
  dst_region: string;
  dst_env: string;
  dst_account_type: string;
  dst_account_channel: string;
  dst_account_id: string;
  status: 'success';
  task_id: string;
  create_time: string;
  update_time: string;
  operator: string;
}

export interface CopyAccountHistoryResp {
  list: CopyAccountHistory[];
  total: number;
}

export interface CopyAccountRegionResp {
  dstRegion: DestRegion[];
  srcRegion: SrcRegion[];
}

export interface DestRegion {
  env: string;
  region: string;
}

export interface SrcRegion {
  env: string;
  region: string;
}

export interface CopyAccountHistoryReq {
  src_region: string;
  src_env: string;
  src_account_type: string;
  src_account_channel: string;
  src_account_id: string;
  dst_region: string;
  dst_env: string;
  dst_account_type: string;
  dst_account_channel: string;
  dst_account_id: string;
}

export interface CopyAccountLogResp {
  job_data: JobData;
}

export interface JobData {
  complete: boolean;
  result: TaskErrorLogRes[];
}

export interface sfActionsRes {
  actions: string[];
}

export interface UpdateResp {
  today_count: number;
  total_count: number;
}

export interface AllRegionResp {
  items: string[];
  total: number;
}

export interface Perm {
  id: number;
  code: string;
  name: string;
  desc: string;
}

export interface Role {
  id: number;
  code: string;
  name: string;
  desc: string;
}

export interface RegionPermRes {
  items: Perm[];
  total: number;
}

export interface RegionRoleRes {
  items: Role[];
  total: number;
}

export interface ApplyPermReq {
  region_name: string;
  apply_type: string;
  role_ids: number[];
  perm_ids: number[];
  desc: string;
}

export interface getApplicationReq {
  page: number;
  size: number;
  status?: number[];
}

export interface Application {
  id: number;
  username: string;
  region_name: string;
  status: number;
  apply_time: string;
  finish_time: string;
  reviewed_by: string;
}

export interface getApplicationRes {
  items: Application[];
  total: number;
}

export interface UserPermRes {
  items: Perm[];
  total: number;
}

export interface UserRoleRes {
  items: Role[];
  total: number;
}

export interface UserRegionRes {
  items: string[];
  total: number;
}

export interface UserRegionPermRes {
  items: Perm[];
  total: number;
}

export interface ApplyActionReq {
  action: string;
}

export interface PatchResp {
  module: string;
  patch_infos: PatchInfoResp[];
}

export interface PatchInfoResp {
  patch_id: string;
  update_time: string;
}

export interface UpdateXmlConfigReq {
  region: string;
  content: string;
}

export interface EnumConfigRes {
  items: EnumConfigData[];
  total: number;
}

export interface EnumConfigData {
  label: string;
  value: string | number;
  desc: string;
  is_default: boolean;
}

export interface GlobalDispatchInfoSearchParams {
  dispatch_url: string;
  channel_id: string;
  sub_channel_id: string;
  client_type: string;
  client_version: string;
}

export interface GlobalDispatchInfoRes {
  region_list: GlobalDispatchInfoRegion[];
}

export interface GlobalDispatchInfoRegion {
  area: number;
  biz: string;
  dispatch_url: string;
  env: number;
  is_recommend: boolean;
  name: string;
  ping_url: string;
  stop_begin_time?: number;
  stop_end_time?: number;
  retcode: number;
  title: string;
}

export interface RegionDispatchInfoSearchParams {
  dispatch_url: string;
  region?: string;
  rsa_ver: string;
  channel_id: string;
  sub_channel_id: string;
  client_type: string;
  client_version: string;
}

export interface RegionDispatchCdnInfo {
  design_data: Map<string, string>;
  game_res: Map<string, string>;
  silence_data: Map<string, string>;
}

export interface GatewayInfo {
  ip: string;
  port: number;
}

export interface RegionDispatchInfoRes {
  cdn_check_url: string;
  cdn_conf_ext: RegionDispatchCdnInfo;
  client_secret_key: string;
  env: number;
  gateway: GatewayInfo;
  region_ext: Map<string, string>;
  region_name: string;
  retcode: number;
  title: string;
}

export interface UserTokenRes {
  items: UserToken[];
  total: number;
}

export interface UserToken {
  id: number;
  token: string;
  expire_time: string;
  is_active: boolean;
  desc: string;
  created_at: string;
}

export interface UserTokenCreation {
  expire_time: number;
  desc: string;
}

export interface UserTokenModification {
  decs?: string;
  is_active?: boolean;
}

export interface Permission {
  id: number;
  name: string;
  code: string;
  desc: string;
  region_name: string;
  is_global: boolean;
  created_at: string; // ISO 时间格式
  updated_at: string; // ISO 时间格式
  created_by: string;
  updated_by: string;
  deleted_at: string | null; // 允许为空
  url: string;
  component: string;
  parent_id: string;
  status: string;
  always_show: string;
  perms_type: string;
  sort_no: number;
  is_route: boolean;
  redirect: string;
  menu_type: number;
  remark: string;
  meta: string;
}

export interface BranchInfo {
  id: number;
  name: string;
  desc: string;
  module_name: string;
  type: string;
  enable: boolean;
  created_at?: string;
}

export interface ServerTimeResp {
  current_time: number;
  offset: number;
}

export interface CopyAccountBlackListReq {
  dst_region: string;
  dst_account_id: string;
  operator: string;
  valid: boolean;
  page_num: number;
  page_size: number;
}

export interface CopyAccountBlackListResp {
  items: CopyAccountBlackList[];
  total: number;
}

export interface CopyAccountBlackList {
  id: number;
  dst_region: string;
  dst_account_type: string;
  dst_account_id: string;
  begin_time: string;
  end_time: string;
  operator: string;
  remark: string;
}

export interface CreateCopyAccountBlackList {
  dst_region: string;
  dst_account_type: string;
  dst_account_id: string;
  begin_time: Dayjs;
  end_time: Dayjs;
  remark: string;
}

export interface DeleteCopyAccountBlackList {
  id: number;
  dst_region: string;
}

export interface TaskLogReq {
  region: string;
  task_id: string;
}

export interface LogRegionsRes {
  items: string[];
  total: number;
}

export interface getRegionSlsInfoReq {
  region: string;
  is_lb: boolean;
}

export interface getRegionSlsInfoRes {
  project: string;
  logstores: string[];
  ticket: string;
  region_id: string;
}

export interface RegionTaskLogReq {
  region: string;
  task_id: string;
  log_level: string;
  query: string[];
  page: number;
  size: number;
}

export interface RegionTaskLogResp {
  items: RegionTaskLogItems[];
  total: number;
}

export interface LogSubtaskItems {
  id: number;
  parent_task_id: string;
  hostname: string;
  ip: string;
  err_msg: string;
  searched_lines: number;
  status: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
}

export interface DownloadRegionLogReq {
  region: string;
  task_id: string;
  only_log: boolean;
}

export interface LogSubtasksResp {
  items: LogSubtaskItems[];
  total: number;
}

export interface RegionTaskLogItems {
  log: string;
  line_number: string;
  file_name: string;
  source: string;
  module: string;
}

export interface CollectRegionLogReq {
  region: string;
  from: string;
  to: string;
  module: string;
  log_file: string;
  search_field: string;
  search_value?: string;
}

export interface CollectLogRes {
  task_id: string;
}

export interface TaskDetailRes {
  task_id: string;
  created_at: string;
  updated_at: string;
  status: number;
  searched_lines: number;
}

export interface LogContextReq {
  region: string;
  hostname: string;
  log_file_path: string;
  log_time: string;
  line_number_from: number;
  line_number_to: number;
  module: string;
}

export interface LogTaskHistoryReq {
  region?: string;
  created_by: string;
  module?: string;
  keyword?: string;
  page: number;
  size: number;
}

export interface LogTaskHistoryResp {
  items: LogTaskHistoryItems[];
  total: number;
}

export interface LogTaskHistoryItems {
  id: number;
  task_id: string;
  status: number;
  region: string;
  search_req: string;
  searched_lines: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
}

export interface GetPreIFixBuildReq {
  search: string;
  page: number;
  size: number;
}

export interface PreIFixBuildItems {
  id: number;
  operator: string;
  url: string;
  created_at: string;
  updated_at: string;
}

export interface GetPreIFixBuildResp {
  items: PreIFixBuildItems[];
  total: number;
}

export interface GetPreIFixDeployReq {
  search: string;
  page: number;
  size: number;
}

export interface GetCurrentPreIFixDeployReq {
  search: string;
  page: number;
  size: number;
}

export interface PreIFixDeployItems {
  id: number;
  client_version_area: string;
  client_version_env: string;
  client_version_platform: string;
  client_version_num: string;
  client_version: string;
  pre_ifix: string;
  status: string;
  msg: string;
  is_del: boolean;
  operator: string;
  created_at: string;
  updated_at: string;
}

export interface GetPreIFixDeployResp {
  items: PreIFixDeployItems[];
  total: number;
}

export interface GetCurrentPreIFixDeployResp {
  items: PreIFixDeployItems[];
  total: number;
}

export interface PreIFixDeployClientVersions {
  client_version_area: string;
  client_version_env: string;
  client_version_platform: string;
  client_version_number: string;
}

export interface CreatePreIFixDeployReq {
  client_version_infos: PreIFixDeployClientVersions[];
  url: string;
  privilege_region: string;
}

export interface DeletePreIFixDeployReq {
  id: number;
  privilege_region: string;
}

export interface GetInnerDevGameResReq {
  branch: string;
  changelist: number;
}

export interface InnerDevGameRes {
  suffix: string;
  branch: string;
}

export interface GetInnerDevGameResResp {
  items: InnerDevGameRes[];
  total: number;
}

export interface GetInnerDevClientDataReq {
  branch: string;
  changelist: number;
}

export interface GetInnerDevClientDataResp {
  items: string[];
  total: number;
}

export interface PostInnerDevGameResReq {
  branch: string;
  suffix: string;
  platform: string;
  region: string;
}

export interface PostInnerDevClientDataReq {
  branch: string;
  suffix: string;
  region: string;
}

export interface PostDrawBoxDownloadReq {
  region: string;
}


export interface MenuItem {
  menuId: number;
  menuName: string;
  title: string;
  category: string;
  meta: string;
  icon: string;
  path: string;
  paths: string;
  menuType: string;
  action: string;
  permission: string;
  parentId: number;
  noCache: boolean;
  breadcrumb: string;
  component: string;
  sort: number;
  visible: string;
  isFrame: string;
  sysApi: any[]; // 这里是空数组，保持 any[]，可以改为具体类型
  apis: any | null; // 这里是 null，使用 any | null
  dataScope: string;
  params: string;
  RoleId: number;
  is_select: boolean;
  createBy: number;
  updateBy: number;
  createdAt: string; // 时间字符串，可以改成 Date 类型
  updatedAt: string;
  children: MenuItem[];
}

export interface Policy {
  id: number;
  ptype: string;
  v0: string;
  v1: string;
  v2: string;
  v3: string;
  v4: string;
  v5: string;
  v6: string;
  v7: string;
}
