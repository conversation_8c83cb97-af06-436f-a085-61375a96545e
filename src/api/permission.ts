import { del, get, post, put } from '@/util/request/request';
import type {
  RegionRes,
  RegionReq,
  AuthRegionReq,
  RegionPermRes,
  RegionRoleRes,
  ApplyPermReq,
  getApplicationReq,
  getApplicationRes,
  UserPermRes,
  UserRoleRes,
  UserRegionRes,
  UserRegionPermRes,
  ApplyActionReq,
  UserTokenRes,
  UserTokenCreation,
  UserTokenModification,
  Permission,
  Policy,
} from '@/api/model';

enum URL {
  // 获取基本信息
  RegionUri = '/api/v1/region',
  PermUri = '/api/v1/perm',
  PermApplyUri = '/api/v1/user/perm/apply',
  ApplicationUri = '/api/v1/perm/apply',
  UserUri = '/api/v1/user',
  UserRegionUri = '/api/v1/user/region',
  UserRegionPermUri = '/api/v1/region/permission',
  ApplyActionUri = '/api/v1/perm/apply/:id/action',
}

const getRegionApi = async (data: RegionReq) =>
  get<RegionRes[]>({ url: URL.RegionUri, params: data });

const PostRegionApi = async (data: AuthRegionReq) => post({ url: URL.RegionUri, data });

// const getRegionPermApi = async (region_name: string) =>
//   get<RegionPermRes>({ url: `${URL.PermUri}/region/${region_name}` });
// const getRegionRoleApi = async (region_name: string) =>
//   get<RegionRoleRes>({ url: `${URL.PermUri}/region/${region_name}/role` });
//
const postApplyPermApi = async (data: ApplyPermReq) => post({ url: URL.PermApplyUri, data });
const getApplicationApi = async (params: getApplicationReq) =>
  get<getApplicationRes>({ url: URL.ApplicationUri, params });

// const getUserPermApi = async (user_id: number) =>
//   get<UserPermRes>({ url: `${URL.UserUri}/${user_id}/perm` });
// const getUserRoleApi = async (user_id: number) =>
//   get<UserRoleRes>({ url: `${URL.UserUri}/${user_id}/role` });
const getUserRegionApi = async () => get<UserRegionRes>({ url: URL.UserRegionUri });
//
const getUserRegionPermApi = async (region_name: string) =>
  get<UserRegionPermRes>({ url: URL.UserRegionPermUri, params: { region: region_name } });

const postApplyActionApi = async (id: number, data: ApplyActionReq) =>
  post({ url: URL.ApplyActionUri.replace(':id', `${id}`), data });

const getUserTokenApi = async (user_id: number) =>
  get<UserTokenRes>({ url: `${URL.UserUri}/${user_id}/token` });

const createUserTokenApi = async (user_id: number, data: UserTokenCreation) =>
  post({ url: `${URL.UserUri}/${user_id}/token`, data });

const putUserTokenApi = async (user_id: number, token_id: number, data: UserTokenModification) =>
  put({ url: `${URL.UserUri}/${user_id}/token/${token_id}`, data });

const deleteUserTokenApi = async (user_id: number, token_id: number) =>
  del({ url: `${URL.UserUri}/${user_id}/token/${token_id}` });

// 获取自己的所有权限
const getMyPolicyApi = async (search: string) =>
  get<Policy>({ url: `${URL.UserUri}/policy`, params: { search: search } });

// 获取自己的所有权限
const getPermissionApi = async (perm_type: string) =>
  get<Policy>({ url: `${URL.UserUri}/permission`, params: { perm_type: perm_type } });

export {
  getRegionApi,
  PostRegionApi,
  getPermissionApi,
  getUserTokenApi,
  createUserTokenApi,
  putUserTokenApi,
  deleteUserTokenApi,
  getUserRegionApi,
  getMyPolicyApi,
  postApplyPermApi,
  getApplicationApi,
  postApplyActionApi,
  getUserRegionPermApi,
};
