import type { GameServerLogReq, ServerErrorLogResp, TaskErrorLogRes } from '@/api/model';
import { get } from '@/util/request/request';

enum URL {
  // 获取基本信息
  getGameServerLogUri = '/api/v1/game_server_log',
  getTaskErrorLogUri = '/api/v1/task_error_log',
}

const getGameServerLogApi = async (data: GameServerLogReq) =>
  get<ServerErrorLogResp>({ url: URL.getGameServerLogUri, params: data });

const getTaskErrorLogApi = async (data: GameServerLogReq) =>
  get<TaskErrorLogRes[]>({ url: URL.getTaskErrorLogUri, params: data });

export { getGameServerLogApi, getTaskErrorLogApi };
