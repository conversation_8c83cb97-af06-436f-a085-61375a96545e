<template>
  <pro-layout
    v-model:collapsed="state.collapsed"
    v-model:selectedKeys="state.selectedKeys"
    v-model:openKeys="state.openKeys"
    :loading="loading"
    :menu-data="rawMenuData"
    :breadcrumb="{ routes: breadcrumb }"
    disable-content-margin
    style="min-height: 100vh"
    iconfont-url="//at.alicdn.com/t/c/font_3990460_bm8dmnin4v.js"
    v-bind="proConfig"
  >
    <!--    图标生成网站 https://www.iconfont.cn/-->
    <template #menuHeaderRender>
      <router-link :to="{ path: '/' }">
        <img src="../assets/logo.png" />
        <h1>绝区零自助平台</h1>
      </router-link>
    </template>
    <template #rightContentRender>
      <RightContent :current-user="currentUser" />
    </template>
    <!-- custom breadcrumb itemRender  -->
    <template #breadcrumbRender="{ route, params, routes }">
      <span v-if="routes.indexOf(route) === routes.length - 1">
        <HeartOutlined />
        {{ route.breadcrumbName }}
      </span>
      <router-link v-else :to="{ path: route.path, params }">
        <SmileOutlined />
        {{ route.breadcrumbName }}
      </router-link>
    </template>
    <SettingDrawer v-model="proConfig" />
    <!--    <RouterView v-slot="{ Component, route }">-->
    <!--      <transition name="slide-left" mode="out-in">-->
    <!--        <component :is="Component" :key="route.path" />-->
    <!--      </transition>-->
    <!--    </RouterView>-->
    <router-view v-slot="{ Component }">
      <transition name="slide-left" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <!--    <router-view></router-view>-->
  </pro-layout>
  <div>
    <FloatButton
      shape="circle"
      :style="{
        right: '94px',
      }"
      href="https://km.mihoyo.com/articleBase/1852/1430993"
      target="_blank"
    >
      <template #icon>
        <QuestionCircleOutlined />
      </template>
    </FloatButton>

    <FloatButton
      shape="square"
      :style="{
        right: '24px',
      }"
      description="技术支持"
      href="https://app.hoyowave.com/service-hub?id=77810113271606"
      target="_blank"
    ></FloatButton>
  </div>
</template>

<script setup lang="ts">
import { useRouter, RouterView, RouterLink } from 'vue-router';
import {
  getMenuData,
  clearMenuItem,
  type RouteContextProps,
  ProLayout,
} from '@ant-design-vue/pro-layout';
import {
  SmileOutlined,
  HeartOutlined,
  QuestionCircleOutlined,
  CustomerServiceOutlined,
} from '@ant-design/icons-vue';
import { reactive, ref, computed, watch, onBeforeMount, onMounted } from 'vue';
import { useUserStore } from '@/stores/modules/user';
import { FloatButton } from 'ant-design-vue';

const router = useRouter();

const userStore = useUserStore();

let { menuData } = getMenuData(clearMenuItem(userStore.getDynamicRoute));

const rawMenuData = ref(menuData);

const updateMenuData = () => {
  if (menuData.length == 0) {
    ({ menuData } = getMenuData(clearMenuItem(router.getRoutes())));
  }
  if (!userStore.getUser.is_admin) {
    // console.log('router.getRoutes(): ', router.getRoutes());
    menuData = menuData.filter(item => !item.meta.is_admin);
  }
  rawMenuData.value = [...menuData];
};

// router.afterEach(() => {
//   updateMenuData();
// });
onBeforeMount(() => {
  updateMenuData();
});

onMounted(async () => {
  // Assume routes are being dynamically added here
  // After routes are added, set isRouteReady to true
});

const state = reactive<Omit<RouteContextProps, 'menuData'>>({
  collapsed: false, // default collapsed
  openKeys: [], // defualt openKeys
  selectedKeys: [], // default selectedKeys
});
const loading = ref(false);
const proConfig = ref({
  layout: 'mix',
  navTheme: 'light',
  fixedHeader: true,
  fixSiderbar: true,
  splitMenus: true,
});
const breadcrumb = computed(() =>
  router.currentRoute.value.matched.concat().map(item => {
    return {
      path: item.path,
      breadcrumbName: item.meta.title || '',
    };
  }),
);
const currentUser = reactive({
  nickname: 'Admin',
  avatar: 'A',
});

watch(
  router.currentRoute,
  () => {
    const matched = router.currentRoute.value.matched.concat();
    state.selectedKeys = matched.filter(r => r.name !== 'index').map(r => r.path);
    state.openKeys = matched
      .filter(r => r.path !== router.currentRoute.value.path)
      .reduce((acc: string[], item: any) => {
        // 展开菜单配置
        if (item && item.meta.openKeys && item.meta.openKeys.length > 0) {
          acc.push(...item.meta.openKeys, item.path);
        } else {
          acc.push(item.path);
        }
        return acc;
      }, []);
  },
  {
    immediate: true,
  },
);

// 监听路由变化
watch(
  () => router.getRoutes(),
  newRoutes => {
    updateMenuData();
  },
  { deep: true, immediate: true },
);

userStore.$subscribe(args => {
  // console.log('userStore.$subscribe: ', args);
  updateMenuData();
});
</script>
