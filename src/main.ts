import '@ant-design-vue/pro-layout/dist/style.css';

import { setupStore } from '@/stores';
import { createApp } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import ProLayout, { PageContainer } from '@ant-design-vue/pro-layout';
import { router, setupRouter } from '@/router';
import App from './App.vue';
import { setupRouterGuard, setupDynamicRoutes } from '@/router/guard';
import VxeTable from 'vxe-table';
import 'vxe-table/lib/style.css';

const app = createApp(App);
setupStore(app);

// 路由守卫
setupRouterGuard(router);
// 必须要提前安装动态路由，或者无法进行页面刷新
await setupDynamicRoutes(router);

setupRouter(app);

// 当路由准备好时再执行挂载( https://next.router.vuejs.org/api/#isready)
// await router.isReady();

app.use(ConfigProvider).use(ProLayout).use(PageContainer).use(VxeTable).mount('#app');
