<template>
  <page-container title="SLS日志">
    <template #content>
      <a-form :model="formState" name="horizontal_login" layout="inline" autocomplete="off">
        <a-form-item label="区服" name="region" style="width: 300px">
          <a-select
            v-model:value="formState.region"
            placeholder="请选择区服"
            :options="regionOptions"
            show-search
          ></a-select>
        </a-form-item>

        <a-form-item label="日志类型" name="sls_type">
          <a-radio-group v-model:value="formState.is_lb" button-style="solid">
            <a-radio-button :value="false">服务器</a-radio-button>
            <a-radio-button :value="true">LB</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-form>
      <a-empty v-if="!slsInfo.ticket" style="height: 1000px; padding: 350px 0">
        <template #description>
          <span>请选择区服</span>
        </template>
      </a-empty>
      <sls-frame
        v-else
        style="margin-top: 20px"
        :ticket="slsInfo.ticket"
        :project="slsInfo.project"
        :logstores="slsInfo.logstores"
        :region-id="slsInfo.region_id"
        v-bind="slsExtraOptions"
      ></sls-frame>
    </template>
  </page-container>
</template>
<script lang="ts" setup>
import { getLogRegionsApi, getRegionSlsInfoApi } from '@/api/log';
import { getRegionSlsInfoRes } from '@/api/model';
import { SelectProps } from 'ant-design-vue';
import { reactive } from 'vue';
import SlsFrame from '../components/SlsFrame/SlsFrame.vue';
interface FormState {
  region?: string;
  is_lb: boolean;
}
const formState = reactive<FormState>({
  is_lb: false,
});
const regionOptions = ref<SelectProps['options']>();
const slsInfo = ref<getRegionSlsInfoRes>({
  project: '',
  logstores: [],
  ticket: '',
  region_id: '',
});

const route = useRoute();
const slsExtraOptions = ref<{
  startTime?: number;
  endTime?: number;
  queryString?: string;
}>({});

function getRouteParams() {
  const region = route.query.region as string;
  const start_time = route.query.start_time as string;
  const end_time = route.query.end_time as string;
  const query_string = route.query.query_string as string;
  const is_lb = (route.query.log_type as string) !== 'server';
  if (
    region &&
    start_time &&
    end_time &&
    query_string &&
    regionOptions.value?.some(item => item.value === region)
  ) {
    formState.region = region;
    formState.is_lb = is_lb;
    slsExtraOptions.value = {
      startTime: parseInt(start_time),
      endTime: parseInt(end_time),
      queryString: query_string,
    };
  }
}

onMounted(async () => {
  await getLogRegionsApi().then(data => {
    regionOptions.value = data.items.map(item => {
      return {
        label: item,
        value: item,
      };
    });
  });
  getRouteParams();
});

watch(formState, () => {
  if (!formState.region) return;
  getRegionSlsInfoApi({
    region: formState.region,
    is_lb: formState.is_lb,
  }).then(data => {
    slsInfo.value = data;
  });
});
</script>
