<template>
  <page-container title="历史记录">
    <template #content>
      <a-form :model="searchForm" name="horizontal_login" autocomplete="off" @finish="handleSearch">
        <a-row :gutter="24">
          <a-col :span="3">
            <a-form-item label="区服" name="region">
              <a-input v-model:value="searchForm.region" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="搜索人" name="created_by" placeholder="请输入">
              <a-input v-model:value="searchForm.created_by" allow-clear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="模块" name="module">
              <enum-selection
                v-model:value="searchForm.module"
                category="module"
                :enable-default="false"
              ></enum-selection>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item label="关键字" name="keyword">
              <a-input v-model:value="searchForm.keyword" placeholder="请输入"></a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-button type="primary" html-type="submit" shape="round" :loading="tableLoading">
              搜索
            </a-button>
          </a-col>
        </a-row>
      </a-form>
      <a-table
        :columns="columns"
        :data-source="datasource"
        row-key="task_id"
        :loading="tableLoading"
        style="min-height: 920px"
        :pagination="{
      total: pagination.total,
      pageSize: pagination.pageSize,
      onChange: (page: number, pageSize: number) => handlePageChange(page, pageSize),
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '30'],
      showTotal: (total: number) => `总共 ${total}条`,
    }"
        @expand="handleExpand"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.key === 'action'">
            <a-button shape="link" size="small" @click="handleView(record)">查看</a-button>
          </template>
          <template v-else-if="column.key === 'status'">
            <template v-if="text === 0">
              <a-tag color="warning">未开始</a-tag>
            </template>
            <template v-else-if="text === 1">
              <a-tag color="processing">
                <template #icon>
                  <sync-outlined :spin="true" />
                </template>
                执行中
              </a-tag>
            </template>
            <template v-else-if="text === 2">
              <a-tag color="success">成功</a-tag>
            </template>
            <template v-else>
              <a-tag color="error">失败</a-tag>
            </template>
          </template>
          <template v-else-if="column.key === 'finished_at'">
            <template v-if="record.status !== 0 && record.status !== 1">
              {{ dayjs(record.updated_at).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
            <template v-else>-</template>
          </template>
          <template v-else-if="column.key === 'module'">
            {{ JSON.parse(text).module }}
          </template>
          <template v-else-if="column.key === 'keyword'">
            {{
              `${JSON.parse(record.search_req).search_field}: ${
                JSON.parse(record.search_req).search_value
              }`
            }}
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <p style="margin: 0">
            <a-descriptions size="small" :column="2" bordered>
              <a-descriptions-item label="模块">
                {{ JSON.parse(record.search_req).module }}
              </a-descriptions-item>
              <a-descriptions-item label="日志文件">
                {{ JSON.parse(record.search_req).log_file }}
              </a-descriptions-item>
              <a-descriptions-item label="搜索字段">
                {{ JSON.parse(record.search_req).search_field }}
              </a-descriptions-item>
              <a-descriptions-item label="搜索值">
                {{ JSON.parse(record.search_req).search_value }}
              </a-descriptions-item>
              <a-descriptions-item
                :label="`搜索日志时间${getRegionTimeZone(record.region)}`"
                :span="2"
              >
                {{ getRegionTime(record.region, JSON.parse(record.search_req).from) }} ~
                {{ getRegionTime(record.region, JSON.parse(record.search_req).to) }}
              </a-descriptions-item>
            </a-descriptions>
          </p>
          <a-table
            style="margin-top: 4px"
            :columns="subtaskColumns"
            :data-source="getSubtasksDatasource(record)"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.key === 'file_names'">
                <div v-for="f in text.split(',')">
                  {{ f }}
                </div>
              </template>
            </template>
          </a-table>
        </template>
      </a-table>
    </template>
  </page-container>
</template>
<script setup lang="ts">
import { getLogTaskHistoryApi, getTaskSubtasksApi } from '@/api/log';
import { LogSubtaskItems, LogTaskHistoryItems } from '@/api/model';
import { useUserStore } from '@/stores/modules/user';
import { SyncOutlined } from '@ant-design/icons-vue';
import EnumSelection from '@/views/components/enum_selection.vue';
import dayjs from 'dayjs';

const userStore = useUserStore();
const router = useRouter();
const columns = [
  { title: '任务ID', dataIndex: 'task_id', key: 'task_id' },
  { title: '任务状态', dataIndex: 'task_status', key: 'status' },
  { title: '搜索行数', dataIndex: 'searched_lines', key: 'searched_lines' },
  { title: '区服', dataIndex: 'region', key: 'region' },
  { title: '模块', dataIndex: 'search_req', key: 'module' },
  { title: '关键字', dataIndex: 'search_req', key: 'keyword' },
  { title: '搜索人', dataIndex: 'created_by', key: 'created_by' },
  {
    title: '开始时间',
    dataIndex: 'created_at',
    key: 'created_at',
    customRender: ({ text }: { text: string }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  { title: '结束时间', key: 'finished_at' },
  { title: '操作', key: 'action' },
];
const subtaskColumns = [
  { title: '搜索主机', dataIndex: 'hostname', key: 'hostname', width: 300 },
  { title: '搜索文件', dataIndex: 'file_names', key: 'file_names' },
];

const tableLoading = ref<boolean>(false);
interface Pagination {
  total: number;
  page: number;
  pageSize: number;
}
interface SearchForm {
  region?: string;
  module?: string;
  keyword?: string;
  created_by: string;
}

const searchForm = reactive<SearchForm>({
  created_by: userStore.getUSerName,
});

const pagination = reactive<Pagination>({
  total: 0,
  page: 1,
  pageSize: 10,
});

const datasource = ref<LogTaskHistoryItems[]>([]);
const subtasksDetailMap = reactive<Map<string, LogSubtaskItems[]>>(new Map());
const handlePageChange = async (currentPage: number, pageSize: number) => {
  pagination.page = currentPage;
  pagination.pageSize = pageSize;
  await getTaskHistory();
};

const getTaskHistory = async () => {
  tableLoading.value = true;
  await getLogTaskHistoryApi({
    region: searchForm.region,
    created_by: searchForm.created_by,
    page: pagination.page,
    size: pagination.pageSize,
    module: searchForm.module,
    keyword: searchForm.keyword,
  }).then(resp => {
    tableLoading.value = false;
    pagination.total = resp.total;
    datasource.value = resp.items;
  });
};

const handleView = (record: LogTaskHistoryItems) => {
  router.push({
    path: '/log/server/view',
    query: {
      ...JSON.parse(record.search_req),
      task_id: record.task_id,
    },
  });
};

const handleSearch = async () => {
  pagination.page = 1;
  await getTaskHistory();
};

const getRegionTimeZone = (region: string) => {
  if (region.endsWith('us')) {
    return '（UTC-05:00）';
  } else if (region.endsWith('eu')) {
    return '（UTC+01:00）';
  } else {
    return '（UTC+08:00）';
  }
};

const getRegionTime = (region: string, time: string) => {
  if (region.endsWith('us')) {
    return dayjs(time).subtract(13, 'hour').format('YYYY-MM-DD HH:mm:ss');
  } else if (region.endsWith('eu')) {
    return dayjs(time).subtract(7, 'hour').format('YYYY-MM-DD HH:mm:ss');
  }
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

const getSubtasksDatasource = (record: LogTaskHistoryItems) => {
  const subtasks = subtasksDetailMap.get(record.task_id);
  if (subtasks) {
    return subtasks;
  }
  return [];
};

const getTaskHostnames = computed(() => {
  return (record: LogTaskHistoryItems) => {
    const subtasks = subtasksDetailMap.get(record.task_id);
    if (subtasks) {
      if (subtasks.length === 0) {
        return '暂无';
      }
      const hostnames = subtasks.map(item => item.hostname).join('、');
      return hostnames;
    }
    return 'loading...';
  };
});

const handleExpand = (expanded: boolean, record: LogTaskHistoryItems) => {
  if (expanded) {
    getTaskSubtasksApi(record.region, record.task_id).then(resp => {
      subtasksDetailMap.set(record.task_id, resp.items);
    });
  }
};

onMounted(async () => {
  await getTaskHistory();
});
</script>
