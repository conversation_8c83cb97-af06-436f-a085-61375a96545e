<template>
  <page-container title="日志查询">
    <template #content>
      <a-form
        :model="searchForm"
        name="horizontal_login"
        autocomplete="off"
        @finish="searchRegionLog"
      >
        <a-row :gutter="24">
          <a-col :span="4">
            <a-form-item label="区服" name="region" :rules="[{ required: true }]">
              <a-select
                v-model:value="searchForm.region"
                placeholder="请选择区服"
                :options="regionOptions"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item
              :label="`日志时间${regionTimeZoneStr}`"
              name="dateRange"
              :rules="[{ validator: validateDateRange, trigger: 'change' }]"
            >
              <a-range-picker
                v-model:value="searchForm.dateRange"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                :show-time="{
                  format: 'HH:mm',
                  defaultValue: [dayjs('00:00', 'HH:mm'), dayjs('23:59:59', 'HH:mm')],
                }"
                :disabled-date="disabledDate"
                @change="handleDateRangeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="模块" name="module" :rules="[{ required: true }]">
              <enum-selection v-model:value="searchForm.module" category="module"></enum-selection>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="日志文件" name="log_file" :rules="[{ required: true }]">
              <enum-selection
                v-model:value="searchForm.log_file"
                :category="logModule"
              ></enum-selection>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="6">
            <a-form-item label="关键字" name="search_keyword">
              <a-input v-model:value="searchForm.search_value" placeholder="请填写对应关键字">
                <template #addonBefore>
                  <a-select v-model:value="searchForm.search_field" style="width: 100px">
                    <a-select-option value="uid">uid</a-select-option>
                    <a-select-option value="aid">aid</a-select-option>
                    <a-select-option value="client_ip">client_ip</a-select-option>
                    <a-select-option value="suid">suid</a-select-option>
                    <a-select-option value="said">said</a-select-option>
                    <a-select-option v-if="isUserServerGroup" value="other">
                      其他
                      <a-tooltip title="搜索当前模块所有机器的日志,对机器可能有性能影响,请小心使用">
                        <ExclamationCircleOutlined />
                      </a-tooltip>
                    </a-select-option>
                  </a-select>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col>
            <a-form-item>
              <a-button type="primary" html-type="submit" shape="round" :loading="taskLoading">
                <template #icon>
                  <CaretRightOutlined />
                </template>
                开始采集
              </a-button>
              <a-button
                :disabled="!taskId || !taskLoading"
                type="danger"
                style="margin-left: 5px"
                shape="round"
                :loading="cancelLoading"
                @click="handleCancel(taskId)"
              >
                <template #icon>
                  <StopOutlined />
                </template>
                停止采集
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <template v-if="taskId && !taskLoading">
        <ServerLogTable :region="searchForm.region ?? ''" :task-id="taskId"></ServerLogTable>
      </template>
      <template v-else>
        <a-empty style="height: 920px; padding: 350px 0">
          <template #description>
            <span>填写表单开始采集任务</span>
          </template>
        </a-empty>
      </template>
    </template>
  </page-container>
</template>
<script lang="ts" setup>
import { CaretRightOutlined, StopOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { cancelTaskApi, collectTaskApi, getLogRegionsApi, getTaskDetailApi } from '@/api/log';
import { message, SelectProps } from 'ant-design-vue';
import ServerLogTable from '@/views/components/ServerLog/ServerLogTable.vue';
import dayjs, { Dayjs } from 'dayjs';
import { TaskDetailRes } from '@/api/model';
import { Rule } from 'ant-design-vue/es/form';
import EnumSelection from '@/views/components/enum_selection.vue';
import { getUserInfoApi } from '@/api/user';

interface SearchForm {
  region?: string;
  module?: string;
  dateRange?: RangeValue;
  log_file?: string;
  search_field?: string;
  search_value?: string;
}

type RangeValue = [Dayjs, Dayjs];

// 判断是否为服务器组同学
const isUserServerGroup = ref<boolean>(false);
const disabledDate = (current: Dayjs) => {
  if (isUserServerGroup.value) {
    return false;
  }
  // 不允许选择昨天之后的时间
  return current > dayjs().endOf('day').subtract(1, 'day');
};

const searchForm = reactive<SearchForm>({
  module: 'gameserver',
  search_field: 'uid',
});
const regionOptions = ref<SelectProps['options']>();
const logModule = computed(() => {
  return `${searchForm.module}_log`;
});
const regionTimeZoneStr = computed(() => {
  if (searchForm.region) {
    if (searchForm.region.endsWith('us')) {
      return '（UTC-05:00）';
    } else if (searchForm.region.endsWith('eu')) {
      return '（UTC+01:00）';
    } else {
      return '（UTC+08:00）';
    }
  }
  return '（UTC+08:00）';
});

const taskLoading = ref<boolean>(false);
const cancelLoading = ref<boolean>(false);
const taskRes = ref<TaskDetailRes>();
const searchRegionLog = async () => {
  taskId.value = '';
  if (
    !searchForm.region ||
    !searchForm.module ||
    !searchForm.log_file ||
    !searchForm.search_field ||
    !searchForm.search_value ||
    !searchForm.dateRange
  ) {
    return;
  }
  taskLoading.value = true;
  var from = searchForm.dateRange[0];
  var to = searchForm.dateRange[1];
  // 根据服务器时区调整时间
  if (searchForm.region.endsWith('us')) {
    from = searchForm.dateRange[0].add(13, 'hour');
    to = searchForm.dateRange[1].add(13, 'hour');
  } else if (searchForm.region.endsWith('eu')) {
    from = searchForm.dateRange[0].add(7, 'hour');
    to = searchForm.dateRange[1].add(7, 'hour');
  }

  await collectTaskApi({
    region: searchForm.region,
    from: from.format('YYYY-MM-DD HH:mm:ss'),
    to: to.format('YYYY-MM-DD HH:mm:ss'),
    module: searchForm.module,
    log_file: searchForm.log_file,
    search_field: searchForm.search_field,
    search_value: searchForm.search_value,
  })
    .then(data => {
      taskId.value = data.task_id;
      waitForTaskFinish(data.task_id)
        .then((result: TaskDetailRes) => {
          taskLoading.value = false;
          taskRes.value = result;
          // console.log(result);
        })
        .catch(error => {
          taskLoading.value = false;
          message.error('任务失败: ', error);
        });
    })
    .catch(() => {
      taskLoading.value = false;
    });
};

const waitForTaskFinish = (
  taskId: string,
  checkInterval = 500,
  timeout = 60000,
): Promise<TaskDetailRes> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    async function checkStatus() {
      if (!searchForm.region) {
        return;
      }
      try {
        await getTaskDetailApi(searchForm.region, taskId).then(data => {
          // 检查任务状态
          if (data.status === 2) {
            resolve(data); // 任务完成
          } else if (data.status === 0 || data.status === 1) {
            // 未完成，继续轮询
            setTimeout(checkStatus, checkInterval);
          } else if (Date.now() - startTime >= timeout) {
            reject(new Error('Polling timed out')); // 超时
          } else {
            reject(new Error('Task failed')); // 任务失败
          }
        });
      } catch (error) {
        reject(error); // 返回错误
      }
    }

    checkStatus(); // 开始轮询
  });
};

const validateDateRange = async (_rule: Rule, value: RangeValue) => {
  if (!value) {
    return Promise.reject('请选择时间范围');
  }
  // console.log(value[0], value[1], value[0].diff(value[1], 'second'));
  if (value[1].diff(value[0], 'second') > 86400 * 7) {
    return Promise.reject('开始时间和结束时间差距不能超过七天');
  } else if (value[1].diff(value[0], 'second') < 60) {
    return Promise.reject('开始时间和结束时间差距不能小于一分钟');
  }

  return Promise.resolve();
};

const handleDateRangeChange = (val: RangeValue) => {
  searchForm.dateRange = [val[0].second(0), val[1].second(0)];
};

const taskId = ref<string>('');

const handleCancel = async (cancelId: string) => {
  if (!searchForm.region) {
    return;
  }
  cancelLoading.value = true;
  await cancelTaskApi(searchForm.region, cancelId).then(() => {
    taskId.value = '';
    cancelLoading.value = false;
    return;
  });
};
const route = useRoute(); // 获取当前路由

const extractParamFromUrl = () => {
  const from = route.query.from as string;
  const to = route.query.to as string;
  const module = route.query.module as string;
  const region = route.query.region as string;
  const log_file = route.query.log_file as string;
  const search_field = route.query.search_field as string;
  const search_value = route.query.search_value as string;
  const task_id = route.query.task_id as string;
  if (from && to && module && region && log_file && search_field && search_value && task_id) {
    var dateRangeFrom = dayjs(from, 'YYYY-MM-DD HH:mm:ss');
    var dateRangeTo = dayjs(to, 'YYYY-MM-DD HH:mm:ss');
    if (region.endsWith('us')) {
      dateRangeFrom = dateRangeFrom.subtract(13, 'hour');
      dateRangeTo = dateRangeTo.subtract(13, 'hour');
    } else if (region.endsWith('eu')) {
      dateRangeFrom = dateRangeFrom.subtract(7, 'hour');
      dateRangeTo = dateRangeTo.subtract(7, 'hour');
    }
    searchForm.dateRange = [dateRangeFrom, dateRangeTo];
    searchForm.module = module;
    searchForm.region = region;
    searchForm.log_file = log_file;
    searchForm.search_field = search_field;
    searchForm.search_value = search_value;
    taskId.value = task_id;
  }
};

onMounted(async () => {
  await getLogRegionsApi().then(data => {
    regionOptions.value = data.items.map(item => {
      return {
        label: item,
        value: item,
      };
    });
  });
  await getUserInfoApi().then(resp => {
    // TODO: 这里先判断orgpath，后续应该改为校验不同权限或者角色实现
    isUserServerGroup.value = resp.org_path.includes('米哈游>绝区零>程序组>服务器组');
  });
  extractParamFromUrl();
});
</script>
