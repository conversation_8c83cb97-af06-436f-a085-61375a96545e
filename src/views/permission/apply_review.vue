<template>
  <page-container :title="route.meta.title">
    <a-card
      style="height: 80vh"
      title="审批工单"
      class="custom-antd-table-wrapper"
      :loading="pageLoading"
    >
      <a-table
        :scroll="{}"
        :columns="columns"
        :row-key="(record:any) => record.id"
        :data-source="dataSource"
        :pagination="pagination"
        :loading="loading"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'status'">
            <template v-if="text === 1">
              <a-tag color="processing">
                <template #icon>
                  <sync-outlined :spin="true" />
                </template>
                审核中
              </a-tag>
            </template>
            <template v-else-if="text === 2">
              <a-tag color="success">已通过</a-tag>
            </template>
            <template v-else-if="text === 3">
              <a-tag color="error">已拒绝</a-tag>
            </template>
          </template>
          <template v-else-if="column.dataIndex === 'apply_perm_role'">
            <a-tag
              v-for="item in record.perm_names.slice(0, 4)"
              :key="item"
              style="margin: 2px 2px"
            >
              {{ item }}
            </a-tag>
            <a-tooltip v-if="record.perm_names.length > 4" :arrow="false">
              <template #title>
                <span>
                  {{ record.perm_names.slice(4).join(', ') }}
                </span>
              </template>
              ... 共{{ record.perm_names.length }}个
            </a-tooltip>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-button
              primary
              type="link"
              :disabled="record.status !== 1 || !user.is_admin"
              @click="handleApprove(record.id)"
            >
              通过
            </a-button>
            <a-button
              danger
              type="text"
              :disabled="record.status !== 1 || !user.is_admin"
              style="margin-left: 10px"
              @click="handleReject(record.id)"
            >
              拒绝
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>
  </page-container>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import { usePagination } from 'vue-request';
import { getApplicationApi, postApplyActionApi } from '@/api/permission';
import dayjs from 'dayjs';
import { getApplicationReq } from '@/api/model';
import { TableProps } from 'ant-design-vue';
import { getUserInfoApi, UserInfoRes } from '@/api/user';
import { SyncOutlined } from '@ant-design/icons-vue';

const user = ref<UserInfoRes>({
  id: 0,
  user_name: '',
  display_name: '',
  mail: '',
  user_id: '',
  thumbnail: '',
  org_path: '',
  is_admin: false,
});
const route = useRoute(); // 获取当前路由
const columns = [
  {
    title: '申请时间',
    dataIndex: 'apply_time',
    customRender: ({ text }: { text: string }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '申请人',
    dataIndex: 'username',
  },
  {
    title: '申请类型',
    dataIndex: 'apply_type',
    customRender: ({ text }: { text: string }) => {
      if (text === 'perm') {
        return '权限';
      } else if (text === 'role') return '权限角色';
    },
  },
  {
    title: '申请权限/策略',
    dataIndex: 'apply_perm_role',
    width: '230px',
  },
  {
    title: '资源/区服',
    dataIndex: 'region_name',
  },
  {
    title: '状态',
    dataIndex: 'status',
    filters: [
      { text: '审核中', value: 1 },
      { text: '已通过', value: 2 },
      { text: '已拒绝', value: 3 },
    ],
    filterMultiple: true,
  },
  {
    title: '申请理由',
    dataIndex: 'desc',
    width: '400px',
    ellipsis: true,
    customRender: ({ text }: { text: string }) => {
      if (text === '') {
        return '-';
      }
      return text;
    },
  },
  {
    title: '审核人',
    dataIndex: 'reviewed_by',
    customRender: ({ text }: { text: string }) => {
      if (text === '') {
        return '-';
      }
      return text;
    },
  },
  {
    title: '完成时间',
    dataIndex: 'finish_time',
    customRender: ({ text }: { text: string }) => {
      if (text === '') {
        return '-';
      }
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];
const total = ref<number>(0);

async function queryApplication(params: getApplicationReq) {
  const res = await getApplicationApi(params);
  total.value = res.total;
  return res.items;
}

const {
  data: dataSource,
  run,
  loading,
  current,
  pageSize,
  refresh,
} = usePagination(queryApplication, {
  pagination: {
    currentKey: 'page',
    pageSizeKey: 'size',
  },
});

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
}));

const pageLoading = ref<boolean>(false);

function getUserInfo() {
  pageLoading.value = true;
  getUserInfoApi().then(data => {
    user.value = data;
    pageLoading.value = false;
  });
}

const handleTableChange: TableProps['onChange'] = (
  pag: { pageSize: number; current: number },
  filters: any,
  sorter: any,
) => {
  run({
    size: pag.pageSize,
    page: pag?.current,
    sortField: sorter.field,
    sortOrder: sorter.order,
    ...filters,
  });
};

function handleApprove(id: number) {
  postApplyActionApi(id, { action: 'approve' }).then(() => {
    refresh();
  });
}

function handleReject(id: number) {
  postApplyActionApi(id, { action: 'deny' }).then(() => {
    refresh();
  });
}

onBeforeMount(async () => {
  await getUserInfo();
});
</script>
<style scoped lang="less">
.custom-antd-table-wrapper {
  ::v-deep(.ant-table-container) {
    height: calc(80vh - 150px);
    overflow-y: auto;
  }
}
</style>
