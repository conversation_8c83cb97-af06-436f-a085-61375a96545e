<template>
  <page-container :title="route.meta.title">
    <a-card :loading="loading" title="申请表单" style="height: 80vh">
      <a-alert
        message="注意：权限中找不到对应的权限时，可以申请策略，如live或beta服拷贝账号、修复服务器时间权限等等。如没有对应的策略或者需要更为复杂的权限策略，请联系页面右下角 ”技术支持“"
        type="info"
      />
      <a-divider />
      <a-form
        :model="perm_apply_form"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 12 }"
        autocomplete="off"
        @finish="handleApply"
      >
        <a-form-item label="申请权限类型" name="permission_type">
          <a-radio-group v-model:value="perm_apply_form.permission_type" @change="getRegionPerms">
            <a-radio-button value="action">权限</a-radio-button>
            <a-radio-button value="policy">策略</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          v-if="perm_apply_form.permission_type === 'action'"
          label="区服名称"
          name="region_name"
          :rules="[{ required: true, message: '请选择区服' }]"
        >
          <a-select
            v-model:value="perm_apply_form.region_name"
            show-search
            placeholder="请选择区服"
            :options="region_options"
          ></a-select>
        </a-form-item>

        <a-form-item
          v-if="perm_apply_form.permission_type === 'action'"
          show-search
          label="权限策略"
          name="perm_ids"
          :rules="[{ required: true, message: '请选择权限' }]"
        >
          <a-select
            v-model:value="perm_apply_form.perm_ids"
            mode="multiple"
            show-search
            placeholder="请选择权限"
            option-filter-prop="label"
            :options="perm_options"
          ></a-select>
        </a-form-item>
        <a-form-item
          v-else
          show-search
          label="权限策略"
          name="perm_ids"
          :rules="[{ required: true, message: '请选择策略' }]"
        >
          <a-select
            v-model:value="perm_apply_form.perm_ids"
            mode="multiple"
            show-search
            placeholder="请选择策略"
            option-filter-prop="label"
            :options="perm_options"
            @change="getPolicyDetail"
          ></a-select>
        </a-form-item>

        <a-form-item
          v-if="perm_apply_form.permission_type === 'policy'"
          label="策略详情"
          name="desc"
        >
          <a-table :data-source="tableData" :columns="columns" />
        </a-form-item>

        <a-form-item
          label="申请理由"
          name="desc"
          :rules="[{ required: true, message: '请输入申请理由' }]"
        >
          <a-textarea v-model:value="perm_apply_form.desc" />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 16 }">
          <a-button type="primary" html-type="submit">提交申请</a-button>
          <a-button style="margin-left: 10px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </page-container>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import { reactive } from 'vue';
import { getPermissionApi, getRegionApi, postApplyPermApi } from '@/api/permission';
import { SelectProps } from 'ant-design-vue';
import { getPolicyApi } from '@/api/policy';

const route = useRoute(); // 获取当前路由
const loading = ref(true);

interface PermApplyForm {
  region_name: string;
  permission_type: string;
  perm_ids: number[];
  desc: string;
}

const perm_apply_form = reactive<PermApplyForm>({
  region_name: '',
  permission_type: 'action',
  perm_ids: [],
  desc: '',
});
const region_options = ref<SelectProps['options']>([]);
const perm_options = ref<SelectProps['options']>([]);
// Table Columns
const columns = [
  { title: 'ID', dataIndex: 'id' },
  { title: '策略名称', dataIndex: 'v0' },
  { title: '资源', dataIndex: 'v1' },
  { title: '权限CODE', dataIndex: 'v2' },
  { title: '效果', dataIndex: 'v3', slotName: 'status' },
  // { title: '操作', slotName: 'action', width: 250 },
];

function getAllRegions() {
  getRegionApi({ name: '' }).then(data => {
    region_options.value = data
      .filter(
        item =>
          !item.name.toLowerCase().startsWith('live') &&
          !item.name.toLowerCase().startsWith('beta') &&
          !item.name.toLowerCase().startsWith('prod'),
      )
      .map(item => {
        return { label: item.name, value: item.name };
      });
  });
}

let perm_id_name_map = {};

function getRegionPerms() {
  perm_apply_form.perm_ids = [];

  getPermissionApi(perm_apply_form.permission_type).then(data => {
    perm_options.value = data.items.map(item => {
      perm_id_name_map[item.id] = item.code; // code可能是action 也有可能是policy name
      return { label: item.name, value: item.id };
    });
  });
}

// Table Data;
const tableData = ref([]);

function getPolicyDetail() {
  tableData.value = [];
  for (let id of perm_apply_form.perm_ids) {
    getPolicyApi({ name: perm_id_name_map[id] }).then(data => {
      // console.log(id, perm_id_name_map[id], data);

      tableData.value.push(...data.list);
    });
  }
}

function handleApply() {
  postApplyPermApi(perm_apply_form).then(() => {
    handleReset();
  });
}

function handleReset() {
  Object.assign(perm_apply_form, {
    region_name: '',
    permission_type: 'action',
    perm_ids: [],
    desc: '',
  });
  role_options.value = [];
  perm_options.value = [];
}

onBeforeMount(async () => {
  loading.value = true;
  getAllRegions();
  getRegionPerms();
  loading.value = false;
});
</script>
