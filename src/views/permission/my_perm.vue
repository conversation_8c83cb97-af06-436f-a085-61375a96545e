<template>
  <div>
    <div v-if="loading" style="text-align: center; padding: 30px 50px">
      <a-spin tip="Loading..." />
    </div>
    <template v-else>
      <a-empty v-if="tableData.length === 0" style="margin-top: 50px">
        <template #description>
          <span>暂无权限</span>
        </template>
        <a-button type="primary" @click="router.push({ name: 'perm-apply' })">
          <template #icon>
            <AppstoreAddOutlined />
          </template>
          去申请
        </a-button>
      </a-empty>
      <template v-else>
        <a-input-search
          v-model:value="regionFilter"
          placeholder="请输入查询内容"
          enter-button
          style="width: 20%; margin-bottom: 20px"
          @search="getUserPerm"
        />
        <a-table :row-selection="rowSelection" :columns="columns" :data-source="tableData">
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'v3'">
              <a-tag v-if="record.v3 == 'allow'" color="green">允许</a-tag>
              <a-tag v-else color="red">拒绝</a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <span>
                <a @click="unbindUserRegion(record.id)">解除授权</a>
              </span>
            </template>
          </template>
        </a-table>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
import { AppstoreAddOutlined } from '@ant-design/icons-vue';
import { getMyPolicyApi } from '@/api/permission';
import { useRouter } from 'vue-router';
import { unbindUserRegionApi } from '@/api/user';
import type { Permission, Policy } from '@/api/model';
import { Table } from 'ant-design-vue';
import { SmileOutlined, DownOutlined } from '@ant-design/icons-vue';

const router = useRouter();
const props = defineProps<{ user_id: number }>();
const loading = ref(true);

const userPerms = ref({});
const regionFilter = ref<string>('');
const filteredResult = computed(() => {
  const res = Object.values(userPerms.value).filter((item: any) => {
    return regionFilter.value === '' || item.region_name.includes(regionFilter.value);
  });
  return res;
});
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'TYPE',
    dataIndex: 'ptype',
  },
  {
    title: '名字',
    dataIndex: 'v0',
  },
  {
    title: '资源',
    dataIndex: 'v1',
  },
  {
    title: '权限CODE',
    dataIndex: 'v2',
  },
  {
    title: '效果',
    dataIndex: 'v3',
  },
  {
    title: 'Action',
    key: 'action',
  },
];
const tableData = ref([]);

const unbindUserRegion = async policyId => {
  unbindUserRegionApi(
    {
      ids: [policyId],
    },
    props.user_id,
  ).then(() => {
    getUserPerm();
  });
};

function getUserPerm() {
  loading.value = true;
  getMyPolicyApi(regionFilter.value).then(resp => {
    tableData.value = resp;
    loading.value = false;
    // console.log('----------------------->', resp, tableData.value);
  });
}

onBeforeMount(() => {
  getUserPerm();
});

const selectedRowKeys = ref<DataType['key'][]>([]); // Check here to configure the default column

const onSelectChange = (changableRowKeys: string[]) => {
  // console.log('selectedRowKeys changed: ', changableRowKeys);
  selectedRowKeys.value = changableRowKeys;
};

const rowSelection = computed(() => {
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
    hideDefaultSelections: true,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
      {
        key: 'odd',
        text: 'Select Odd Row',
        onSelect: changableRowKeys => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changableRowKeys.filter((_key, index) => {
            if (index % 2 !== 0) {
              return false;
            }
            return true;
          });
          selectedRowKeys.value = newSelectedRowKeys;
        },
      },
      {
        key: 'even',
        text: 'Select Even Row',
        onSelect: changableRowKeys => {
          let newSelectedRowKeys = [];
          newSelectedRowKeys = changableRowKeys.filter((_key, index) => {
            if (index % 2 !== 0) {
              return true;
            }
            return false;
          });
          selectedRowKeys.value = newSelectedRowKeys;
        },
      },
    ],
  };
});
</script>
