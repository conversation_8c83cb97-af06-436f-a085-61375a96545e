<template>
  <page-container :title="route.meta.title">
    <a-card :loading="loading" style="height: 80vh" title="权限详情">
      <a-descriptions title="用户信息">
        <a-descriptions-item label="用户头像">
          <a-avatar shape="square" :size="28">
            <template #icon>
              <img :src="user.thumbnail" alt="" />
            </template>
          </a-avatar>
        </a-descriptions-item>
        <a-descriptions-item label="用户名">{{ user.user_name }}</a-descriptions-item>
        <a-descriptions-item label="显示名称">{{ user.display_name }}</a-descriptions-item>
        <a-descriptions-item label="组织信息">{{ user.org_path }}</a-descriptions-item>
        <a-descriptions-item label="是否为超管">
          {{ user.is_admin ? '是' : '否' }}
        </a-descriptions-item>
      </a-descriptions>
      <a-tabs v-if="!loading" v-model:activeKey="activeKey">
        <a-tab-pane key="perm" tab="权限">
          <my-perm v-if="activeKey === 'perm'" :user_id="user.id"></my-perm>
        </a-tab-pane>
        <a-tab-pane key="token" tab="我的Token">
          <my-token
            v-if="activeKey === 'token'"
            :user-id="user.id"
            :username="user.user_name"
          ></my-token>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </page-container>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import { getUserInfoApi, UserInfoRes } from '@/api/user';
import MyPerm from '@/views/permission/my_perm.vue';
import MyToken from '@/views/permission/my_token.vue';

const route = useRoute(); // 获取当前路由
const user = ref<UserInfoRes>({
  id: 0,
  user_name: '',
  display_name: '',
  mail: '',
  user_id: '',
  thumbnail: '',
  org_path: '',
  is_admin: false,
});
const loading = ref(true);
const activeKey = ref<string>('perm');
function getUserInfo() {
  loading.value = true;
  getUserInfoApi().then(data => {
    user.value = data;
    loading.value = false;
  });
}

onBeforeMount(async () => {
  await getUserInfo();
});
</script>
