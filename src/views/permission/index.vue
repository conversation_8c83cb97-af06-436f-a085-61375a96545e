<!-- deprecated -->
<template>
  <page-container :title="`${route.meta.title} `">
    <div>
      <span style="margin-left: 8px">
        <template v-if="hasSelected">
          {{ `共计选择了 ${selectedRowKeys.length} 个私服` }}
        </template>
      </span>
      <a-row style="margin-bottom: 16px; float: right">
        <a-space>
          <a-col>
            <a-input-search
              v-model:value="searchText"
              style="width: 100%"
              placeholder="请输入区服名字进行查询"
              @search="getRegion"
            />
          </a-col>
          <a-col>
            <a-popconfirm
              title="确认授权私服权限?"
              ok-text="Yes"
              cancel-text="No"
              :disabled="!hasSelected"
              @confirm="PostRegion"
            >
              <a-button type="primary" :disabled="!hasSelected" :loading="loading">
                申请权限
              </a-button>
            </a-popconfirm>
          </a-col>
        </a-space>
      </a-row>
      <a-table
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          type: 'radio',
        }"
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        row-key="name"
      ></a-table>
    </div>
  </page-container>
</template>
<script lang="ts">
import { computed, defineComponent, reactive, toRefs, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getRegionApi, PostRegionApi } from '@/api/permission';
import type { RegionRes } from '@/api/model';
import dayjs from 'dayjs';

const columns = [
  {
    title: '私服名',
    dataIndex: 'name',
  },
  {
    title: 'Environment',
    dataIndex: 'environment',
  },
  {
    title: 'op_server',
    dataIndex: 'op_server',
  },
  {
    title: '代码分支',
    dataIndex: 'branch',
  },
  {
    title: '数值分支',
    dataIndex: 'data_branch',
  },
  {
    title: 'Description',
    dataIndex: 'description',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    customRender: ({ text, record, index, column }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '修改时间',
    dataIndex: 'update_time',
    customRender: ({ text, record, index, column }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
  },
];

export default defineComponent({
  setup() {
    type Key = string;

    const dataSource = ref<RegionRes[]>([]);
    const searchText = ref<string>('');
    const state = reactive<{
      selectedRowKeys: Key[];
      loading: boolean;
    }>({
      selectedRowKeys: [], // Check here to configure the default column
      loading: false,
    });

    const hasSelected = computed(() => state.selectedRowKeys.length > 0);

    function getRegion() {
      const params = { name: searchText.value };
      state.loading = true;
      getRegionApi(params)
        .then(data => {
          console.info(data);
          dataSource.value = data;
        })
        .finally(() => {
          state.loading = false;
        });
    }

    onMounted(() => {
      // console.log(`the component is now mounted.`);
      getRegion();
    });

    const PostRegion = () => {
      state.loading = true;
      // ajax request after empty completing
      PostRegionApi({ region_names: state.selectedRowKeys }).then(data => {
        state.loading = false;
      });
    };
    const onSelectChange = (selectedRowKeys: any, selectedRows: any) => {
      console.log(
        'selectedRowKeys changed: ',
        selectedRowKeys,
        'searchText.value: ',
        searchText.value,
        'selectedRows: ',
        selectedRows,
      );
      state.selectedRowKeys = selectedRowKeys;
    };

    const route = useRoute(); // 获取当前路由
    return {
      dataSource,
      columns,
      hasSelected,
      ...toRefs(state),
      // func
      PostRegion,
      onSelectChange,
      searchText,
      route,
      getRegion,
    };
  },
});
</script>
