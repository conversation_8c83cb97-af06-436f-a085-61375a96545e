<template>
  <div>
    <a-button type="primary" @click="showModal">创建token</a-button>
    <a-modal
      v-model:open="open"
      title="创建token"
      ok-text="创建"
      cancel-text="取消"
      @ok="handleCreation"
    >
      <a-form ref="formRef" :model="create_token_form" :label-col="{ span: 4 }">
        <a-form-item
          label="过期时间"
          name="expire_time"
          :rules="[{ required: true, message: '请选择过期时间' }]"
        >
          <a-date-picker
            v-model:value="create_token_form.expire_time"
            show-time
            :presets="presets"
          />
        </a-form-item>
        <a-form-item label="备注" name="desc">
          <a-textarea v-model:value="create_token_form.desc" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-table
      :data-source="datasource"
      :columns="columns"
      :loading="loading"
      style="margin-top: 10px"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'operation'">
          <a-button v-if="record.is_active" type="link" @click="handleDisable(record.id)">
            禁用
          </a-button>
          <a-button v-else type="link" @click="handleEnable(record.id)">启用</a-button>
          <a-button danger type="text" @click="handleDelete(record.id)">删除</a-button>
        </template>
        <template v-else-if="column.dataIndex === 'is_active'">
          <template v-if="record.is_active">
            <a-tag color="success">是</a-tag>
          </template>
          <template v-else>
            <a-tag color="error">否</a-tag>
          </template>
        </template>
        <template v-if="column.dataIndex === 'token'">
          <a-tooltip
            :arrow="false"
            placement="topLeft"
            :title="record.token"
            :overlay-inner-style="{ width: '500px' }"
          >
            <span class="ellipsis">
              {{ record.token }}
            </span>
          </a-tooltip>
          <a-button
            type="text"
            :icon="h(CopyOutlined)"
            style="vertical-align: bottom"
            @click="handleCopy(record.token)"
          />
        </template>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
import { UserToken } from '@/api/model';
import {
  createUserTokenApi,
  deleteUserTokenApi,
  getUserTokenApi,
  putUserTokenApi,
} from '@/api/permission';
import { FormInstance, message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { ref } from 'vue';
import { h } from 'vue';
import { CopyOutlined } from '@ant-design/icons-vue';

const open = ref<boolean>(false);
const loading = ref<boolean>(false);

const props = defineProps<{
  userId: number;
  username: string;
}>();

interface TokenForm {
  expire_time: Dayjs | undefined;
  desc: string;
}

const datasource = ref<UserToken[]>();
const create_token_form = ref<TokenForm>({
  expire_time: undefined,
  desc: '',
});

const columns = [
  {
    title: 'token',
    dataIndex: 'token',
    width: '500px',
  },
  {
    title: '是否启用',
    dataIndex: 'is_active',
  },
  {
    title: '备注',
    dataIndex: 'desc',
    customRender: ({ text }: { text: string }) => {
      return text ? text : '-';
    },
  },
  {
    title: '过期时间',
    dataIndex: 'expire_time',
    customRender: ({ text }: { text: string }) => {
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    customRender: ({ text }: { text: string }) => {
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];

const presets = ref([
  { label: '1天', value: dayjs().add(1, 'd') },
  { label: '7天', value: dayjs().add(7, 'd') },
  { label: '30天', value: dayjs().add(30, 'd') },
]);

const getToken = async () => {
  loading.value = true;
  await getUserTokenApi(props.userId).then(data => {
    datasource.value = data.items;
  });
  loading.value = false;
};

onBeforeMount(async () => {
  await getToken();
});

const showModal = () => {
  open.value = true;
};

const formRef = ref<FormInstance>();

const handleCreation = async () => {
  if (!formRef.value) return;
  formRef.value
    .validateFields()
    .then(async () => {
      if (!create_token_form.value.expire_time) {
        return;
      }
      await createUserTokenApi(props.userId, {
        desc: create_token_form.value.desc,
        expire_time: create_token_form.value.expire_time.unix(),
      }).then(() => {
        open.value = false;
        Object.assign(create_token_form.value, {
          expire_time: undefined,
          desc: '',
        });
      });
      await getToken();
    })
    .catch(() => {
      // console.log('validate failed');
    });
};

// http下无法访问clipboard api，这时需要以下面这种方式来访问剪贴板
const fallbackCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed'; // 避免滚动到视图
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    const msg = successful ? 'successful' : 'unsuccessful';
    // console.log('Fallback: Copying text command was ' + msg);
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
};

const handleCopy = async (value: string) => {
  if (!navigator.clipboard) {
    fallbackCopyToClipboard(value);
  } else {
    navigator.clipboard.writeText(value);
  }
  message.info('已复制token到剪贴板');
};

const handleDisable = async (tokenId: number) => {
  await putUserTokenApi(props.userId, tokenId, { is_active: false }).then(() => {
    getToken();
  });
};

const handleEnable = async (tokenId: number) => {
  await putUserTokenApi(props.userId, tokenId, { is_active: true }).then(() => {
    getToken();
  });
};

const handleDelete = async (tokenId: number) => {
  await deleteUserTokenApi(props.userId, tokenId).then(() => {
    getToken();
  });
};
</script>
<style scoped>
.ellipsis {
  display: inline-block; /* 设置为 inline-block 或 block */
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
  width: 400px;
}
</style>
