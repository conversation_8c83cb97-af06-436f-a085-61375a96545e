<template>
  <div>
    <div v-if="loading" style="text-align: center; padding: 30px 50px">
      <a-spin tip="Loading..." />
    </div>
    <template v-else>
      <a-empty
        v-if="userRoles.length === 0 || Object.keys(userRoles).length === 0"
        style="margin-top: 50px"
      >
        <template #description>
          <span>暂无权限角色</span>
        </template>
        <a-button type="primary" @click="router.push({ name: 'perm-apply' })">
          <template #icon>
            <AppstoreAddOutlined />
          </template>
          去申请
        </a-button>
      </a-empty>
      <template v-else>
        <a-input-search
          v-model:value="regionFilter"
          placeholder="请输入区服名称"
          enter-button
          style="width: 20%; margin-bottom: 20px"
        />
        <a-row :gutter="16" style="height: calc(80vh - 350px); width: 100%; overflow-y: auto">
          <a-col
            v-for="item in filteredResult"
            :key="item.region_name"
            :span="6"
            style="margin-bottom: 12px"
          >
            <a-card :title="item.region_name">
              <template #extra>
                <a-button type="text" danger @click="unbindUserRegion(item.region_name)">
                  解绑
                </a-button>
              </template>
              <a-tag
                v-for="p in item.roles"
                :key="item.id"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ p.name }}
              </a-tag>
            </a-card>
          </a-col>
        </a-row>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router';
import { AppstoreAddOutlined } from '@ant-design/icons-vue';
import { unbindUserRegionApi } from '@/api/user';
const router = useRouter();

const props = defineProps<{ user_id: number }>();
const loading = ref(true);

const userRoles = ref([]);
const regionFilter = ref<string>('');
const filteredResult = computed(() => {
  const res = Object.values(userRoles.value).filter((item: any) => {
    return regionFilter.value === '' || item.region_name.includes(regionFilter.value);
  });
  return res;
});

const unbindUserRegion = async (region: string) => {
  unbindUserRegionApi(
    {
      region_name: region,
    },
    props.user_id,
  ).then(() => {
    getUserRole();
  });
};

function getUserRole() {}

onBeforeMount(() => {
  getUserRole();
});
</script>
