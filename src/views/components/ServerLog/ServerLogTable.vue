<template>
  <div>
    <a-divider></a-divider>
    <a-descriptions :title="props.hideTitle ? '' : '采集日志'" size="small">
      <a-descriptions-item label="任务ID">{{ props.taskId }}</a-descriptions-item>
      <a-descriptions-item label="采集行数">{{ searchedLines }}</a-descriptions-item>
    </a-descriptions>
    <a-form :model="searchForm" layout="inline" autocomplete="off">
      <a-form-item label="日志级别" name="log_level" style="width: 300px">
        <a-select
          v-model:value="searchForm.log_level"
          :options="logLevelOptions"
          @change="getRegionTaskLogs"
        ></a-select>
      </a-form-item>

      <a-form-item label="日志关键字" name="content">
        <span
          v-for="(_, index) in searchForm.query"
          :key="index"
          :style="{ 'margin-left': index === 0 ? 0 : '8px' }"
        >
          <a-input
            v-model:value="searchForm.query[index]"
            style="width: 230px"
            :placeholder="`请输入关键词${index + 1}`"
          />
        </span>
        <a-button
          v-if="searchForm.query.length < 5"
          type="dashed"
          style="margin-left: 8px"
          @click="addKeyword"
          size="small"
        >
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>
        <a-button
          v-if="searchForm.query.length > 1"
          type="dashed"
          size="small"
          danger
          style="margin-left: 8px"
          @click="removeKeyword(searchForm.query.length - 1)"
        >
          <template #icon><MinusOutlined /></template>
          删除
        </a-button>
      </a-form-item>

      <a-button type="primary" @click="handleSearch" shape="round" style="margin-right: 8px">
        <template #icon><SearchOutlined /></template>
        搜索
      </a-button>

      <a-button type="primary" @click="showDownloadModal" shape="round">
        <template #icon><DownloadOutlined /></template>
        下载
      </a-button>
    </a-form>

    <vxe-table
      :data="datasource"
      size="small"
      :loading="tableLoading"
      :menu-config="menuConfig"
      :expand-config="expandConfig"
      :min-height="tableHeight"
      :max-height="tableHeight"
      style="margin-top: 6px"
      @menu-click="contextMenuClickEvent"
    >
      <vxe-column type="expand" width="60">
        <template #content="{ row }">
          <a-descriptions size="small" :column="1" style="margin: 10px 10px" bordered>
            <a-descriptions-item label="模块">{{ row.module }}</a-descriptions-item>
            <a-descriptions-item label="来源主机">{{ row.source }}</a-descriptions-item>
            <a-descriptions-item label="文件名">
              <a-typography-text copyable>{{ row.file_name }}</a-typography-text>
            </a-descriptions-item>
          </a-descriptions>
        </template>
      </vxe-column>
      <vxe-column field="line_number" title="行数" width="80"></vxe-column>
      <vxe-column field="log" title="日志">
        <template #default="{ row }">
          <span
            :style="{
              whiteSpace: row.file_name.includes('packet.log') ? 'pre' : 'normal',
            }"
          >
            <span v-html="highlightText(row.log.trim())"></span>
          </span>
        </template>
      </vxe-column>
      <template #empty>
        <div>暂无数据</div>
        <a-button type="link" @click="handleSearch">查看最新日志</a-button>
      </template>
    </vxe-table>
    <vxe-pager
      v-model:currentPage="pagination.page"
      v-model:pageSize="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[100, 500, 1000]"
      @page-change="handlePageChange"
    ></vxe-pager>
  </div>
  <server-log-context
    :visible="contextModalVisible"
    :region="props.region"
    :file-path="contextProps.filepath"
    :hostname="contextProps.hostname"
    :module="contextProps.module"
    :start-line-number="contextProps.startLineNumber"
    :log-time="contextProps.log_time"
    @toggle-modal="toggleModal"
  ></server-log-context>
  <!-- 添加下载模态框 -->
  <a-modal
    v-model:open="downloadModalVisible"
    title="下载日志"
    @ok="handleDownload"
    okText="下载"
    cancelText="取消"
  >
    <a-form layout="vertical">
      <a-checkbox v-model:checked="onlyLog">仅下载日志内容（不包含元数据）</a-checkbox>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import {
  SearchOutlined,
  PlusOutlined,
  MinusOutlined,
  DownloadOutlined,
} from '@ant-design/icons-vue';
import ServerLogContext from './ServerLogContext.vue';
import { message, SelectProps } from 'ant-design-vue';
import { RegionTaskLogItems } from '@/api/model';
import { getRegionTaskLogApi, getTaskDetailApi, downloadTaskLogApi } from '@/api/log';
const props = withDefaults(
  defineProps<{
    region: string;
    taskId: string;
    tableHeight?: number;
    viewContext?: boolean;
    hideTitle?: boolean;
  }>(),
  {
    tableHeight: 720,
    viewContext: true,
    hideTitle: false,
  },
);

const searchedLines = ref<number>(0);

interface Pagination {
  total: number;
  page: number;
  pageSize: number;
}
interface SearchForm {
  log_level: string;
  query: string[];
}

const logLevelOptions = ref<SelectProps['options']>([
  {
    value: 'Debug',
    label: 'Debug 详细日志',
  },
  {
    value: 'Info',
    label: 'Info 信息日志',
  },
  {
    value: 'Warning',
    label: 'Warning 警告日志',
  },
  {
    value: 'Error',
    label: 'Error 错误日志',
  },
  {
    value: 'All',
    label: '所有日志',
  },
]);

const searchForm = reactive<SearchForm>({
  log_level: 'All',
  query: [''],
});

const pagination = reactive<Pagination>({
  total: 0,
  page: 1,
  pageSize: 500,
});

const currentQuery = ref<string[]>([]);

const tableLoading = ref<boolean>(false);
const highlightText = (text: string) => {
  const keyword = currentQuery.value.join('|'); // 搜索关键字
  if (!text || !keyword) return text;
  return text.replace(new RegExp(`(${keyword})`, 'gi'), '<span class="highlight">$1</span>');
};

// http下无法访问clipboard api，这时需要以下面这种方式来访问剪贴板
const fallbackCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.position = 'fixed'; // 避免滚动到视图
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand('copy');
    const msg = successful ? 'successful' : 'unsuccessful';
    // console.log('Fallback: Copying text command was ' + msg);
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
};

const handleCopy = async (value: string) => {
  if (!navigator.clipboard) {
    fallbackCopyToClipboard(value);
  } else {
    navigator.clipboard.writeText(value);
  }
  message.success('已复制日志到剪贴板');
};

const datasource = ref<RegionTaskLogItems[]>([]);

const getRegionTaskLogs = async () => {
  tableLoading.value = true;
  await getRegionTaskLogApi({
    region: props.region,
    task_id: props.taskId,
    log_level: searchForm.log_level,
    query: searchForm.query,
    page: pagination.page,
    size: pagination.pageSize,
  }).then(resp => {
    tableLoading.value = false;
    pagination.total = resp.total;
    datasource.value = resp.items;
  });
};

const handlePageChange = async ({
  currentPage,
  pageSize,
}: {
  currentPage: number;
  pageSize: number;
}) => {
  pagination.page = currentPage;
  pagination.pageSize = pageSize;
  await getRegionTaskLogs();
};

const handleSearch = async () => {
  // 重置页数
  pagination.page = 1;
  pagination.pageSize = 500;
  currentQuery.value = searchForm.query.filter(item => item !== '');
  await getRegionTaskLogs();
};

const menuConfig = reactive({
  enabled: true,
  header: {},
  body: {
    options: [
      [
        {
          code: 'copy',
          name: '复制（Ctrl+C）',
        },
        props.viewContext
          ? {
              code: 'show-context',
              name: '查看上下文',
            }
          : {},
      ],
    ],
  },
});

const contextMenuClickEvent = ({ menu, row, column }: { menu: any; row: any; column: any }) => {
  switch (menu.code) {
    case 'copy':
      // 示例
      if (row && column) {
        handleCopy(row[column.field]);
      }
      break;
    case 'show-context':
      if (row && column) {
        contextProps.filepath = row['file_name'];
        contextProps.module = row['module'];
        contextProps.hostname = row['source'].replace(/\s/g, '');
        contextProps.startLineNumber = Number(row['line_number']);
        contextProps.log_time = row['log_time'];
        contextModalVisible.value = true;
      }
  }
};

const expandConfig = reactive({
  showIcon: true,
  iconOpen: 'vxe-icon-square-minus',
  iconClose: 'vxe-icon-square-plus',
});

const contextModalVisible = ref<boolean>(false);
const toggleModal = () => {
  contextModalVisible.value = !contextModalVisible.value;
};

const getTaskDetail = async () => {
  await getTaskDetailApi(props.region, props.taskId).then(data => {
    searchedLines.value = data.searched_lines;
  });
};

const contextProps = reactive({
  filepath: '',
  hostname: '',
  module: '',
  log_time: '',
  startLineNumber: 0,
});

onMounted(async () => {
  await getTaskDetail();
  await getRegionTaskLogs();
});

// 添加关键字输入框
const addKeyword = () => {
  searchForm.query.push('');
};

// 移除关键字输入框
const removeKeyword = (index: number) => {
  searchForm.query.splice(index, 1);
};

// 下载相关状态
const downloadModalVisible = ref<boolean>(false);
const onlyLog = ref<boolean>(true);

// 显示下载模态框
const showDownloadModal = () => {
  downloadModalVisible.value = true;
};

const downloadFile = (data: any) => {
  // 创建 Blob 对象
  const blob = new Blob([data], { type: 'text/plain' });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;

  // 设置文件名
  const fileName = `log_${props.region}_${props.taskId}_${new Date().getTime()}.txt`;
  link.setAttribute('download', fileName);

  // 添加到文档并触发点击
  document.body.appendChild(link);
  link.click();

  // 清理
  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

// 处理下载请求
const handleDownload = async () => {
  try {
    const response = await downloadTaskLogApi({
      region: props.region,
      task_id: props.taskId,
      only_log: onlyLog.value,
    });
    downloadFile(response.data);

    message.success('日志下载成功');
    downloadModalVisible.value = false;
  } catch (error) {
    message.error('下载失败，请稍后重试');
    console.error('下载错误:', error);
  }
};
</script>
<style>
.highlight {
  background-color: #ff0;
}
</style>
