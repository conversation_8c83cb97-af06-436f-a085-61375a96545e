<template>
  <a-modal
    centered
    ok-text=""
    cancel-text="返回"
    style="width: 1500px"
    :ok-button-props="{ style: { display: 'none' } }"
    :keyboard="false"
    :open="props.visible"
    :mask-closable="false"
    title="查看日志上下文"
    @cancel="handleCancel"
  >
    <a-descriptions size="small" :column="3" bordered style="margin-bottom: 10px">
      <a-descriptions-item label="区服">{{ props.region }}</a-descriptions-item>
      <a-descriptions-item label="主机">{{ props.hostname }}</a-descriptions-item>
      <a-descriptions-item label="起始行数">{{ props.startLineNumber }}</a-descriptions-item>
      <a-descriptions-item label="文件名">{{ props.filePath }}</a-descriptions-item>
    </a-descriptions>
    <a-form layout="inline">
      <a-form-item label="日志类型">
        <a-select
          ref="select"
          v-model:value="logType"
          style="width: 187px"
          :options="logTypeOptions"
        ></a-select>
      </a-form-item>
      <a-form-item label="关键字">
        <a-input v-model:value="query"></a-input>
      </a-form-item>
      <a-button
        type="dashed"
        style="margin-left: 5px"
        shape="round"
        @click="scrollToStart(props.startLineNumber)"
      >
        回到初始位置
      </a-button>
      <a-button
        type="primary"
        style="margin-left: 5px"
        shape="round"
        :loading="previousLoading"
        :disabled="lineNumberFrom === 1"
        @click="loadPrevious"
      >
        <template #icon>
          <DoubleLeftOutlined />
        </template>
        向前加载500行
      </a-button>
      <a-button
        type="primary"
        style="margin-left: 5px"
        shape="round"
        :loading="nextLoading"
        :disabled="hasNoNextLines"
        @click="loadNext"
      >
        <template #icon>
          <DoubleRightOutlined />
        </template>
        向后加载500行
      </a-button>
    </a-form>
    <vxe-table
      ref="tableRef"
      :data="filteredDatasource"
      size="small"
      :loading="tableLoading"
      min-height="720"
      max-height="720"
      style="margin-top: 10px"
    >
      <vxe-column field="line_number" title="行数" width="80"></vxe-column>
      <vxe-column field="log" title="日志">
        <template #default="{ row }">
          <template v-if="Number(row.line_number) === props.startLineNumber">
            <span
              :style="{
                'font-weight': 'bold',
                whiteSpace: row.file_name.includes('packet.log') ? 'pre' : 'normal',
              }"
            >
              <span v-html="highlightText(row.log.trim())"></span>
            </span>
          </template>
          <span
            v-else
            :style="{
              whiteSpace: row.file_name.includes('packet.log') ? 'pre' : 'normal',
            }"
          >
            <span v-html="highlightText(row.log.trim())"></span>
          </span>
        </template>
      </vxe-column>
    </vxe-table>
  </a-modal>
</template>

<script setup lang="ts">
import { getLogContextApi } from '@/api/log';
import { RegionTaskLogItems } from '@/api/model';
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons-vue';
import { SelectProps } from 'ant-design-vue';

const props = defineProps<{
  visible: boolean;
  region: string;
  filePath: string;
  hostname: string;
  module: string;
  logTime: string;
  startLineNumber: number;
}>();
const emit = defineEmits<{
  (e: 'toggle-modal'): void;
}>();

const datasource = ref<RegionTaskLogItems[]>([]);
const tableLoading = ref<boolean>(false);
const previousLoading = ref<boolean>(false);
const nextLoading = ref<boolean>(false);
const hasNoNextLines = ref<boolean>(false);
const lineNumberFrom = ref<number>(0);
const lineNumberTo = ref<number>(0);
const query = ref<string>('');
const logType = ref<string>('ALL');
const tableRef = ref<any>();
const logTypeOptions = ref<SelectProps['options']>([
  {
    value: 'ALL',
    label: 'ALL 所有日志',
  },
  {
    value: 'DEBUG',
    label: 'DEBUG 详细日志',
  },
  {
    value: 'INFO',
    label: 'INFO 信息日志',
  },
  {
    value: 'WARNING',
    label: 'WARNING 警告日志',
  },
  {
    value: 'ERROR',
    label: 'ERROR 错误日志',
  },
]);
const filteredDatasource = computed(() => {
  return datasource.value.filter(
    item =>
      (query.value === '' || item.log.includes(query.value)) &&
      (logType.value === 'ALL' || item.log.includes(`LOG_${logType.value}`)),
  );
});

const highlightText = (text: string) => {
  const keyword = query.value; // 搜索关键字
  if (!text || !keyword) return text;
  return text.replace(new RegExp(`(${keyword})`, 'gi'), '<span class="highlight">$1</span>');
};

// 监听 props.value 的变化
watch(
  () => [props.filePath, props.startLineNumber],
  async ([newFilepath, newStartLineNumber], [oldFilepath, oldStartLineNumber]) => {
    if (
      newFilepath !== oldFilepath ||
      (typeof newStartLineNumber === 'number' &&
        datasource.value.map(item => item.line_number).indexOf(`${newStartLineNumber}`) === -1)
    ) {
      tableLoading.value = true;
      await getLogContext(Math.max(props.startLineNumber - 500, 1), props.startLineNumber + 499)
        .then(data => {
          lineNumberFrom.value = Math.max(props.startLineNumber - 500, 1);
          lineNumberTo.value = props.startLineNumber + 499;
          datasource.value = data;
          tableLoading.value = false;
        })
        .catch(err => {
          tableLoading.value = false;
        });
    }
    if (newStartLineNumber != oldStartLineNumber) {
      setTimeout(() => {
        scrollToStart(Math.max((newStartLineNumber as number) - 7, 1));
      }, 200); // 等待加载完成后再滚动
    }
  },
);

const getLogContext = async (from: number, to: number): Promise<RegionTaskLogItems[]> => {
  let items: RegionTaskLogItems[] = [];
  await getLogContextApi({
    region: props.region,
    hostname: props.hostname,
    log_file_path: props.filePath,
    line_number_from: from,
    line_number_to: to,
    module: props.module,
    log_time: props.logTime,
  }).then(resp => {
    items = resp.items;
  });
  return items;
};

const loadPrevious = () => {
  previousLoading.value = true;
  getLogContext(Math.max(lineNumberFrom.value - 500, 0), lineNumberFrom.value - 1)
    .then(items => {
      previousLoading.value = false;
      datasource.value = items.concat(datasource.value.slice());
      lineNumberFrom.value = Number(datasource.value[0].line_number);
    })
    .catch(err => {
      previousLoading.value = false;
    });
};

const loadNext = () => {
  nextLoading.value = true;
  getLogContext(lineNumberTo.value + 1, lineNumberTo.value + 500)
    .then(items => {
      nextLoading.value = false;
      datasource.value = datasource.value.slice().concat(items);
      lineNumberTo.value = Number(datasource.value.slice(-1)[0].line_number);
      if (items.length < 500) {
        hasNoNextLines.value = true;
      }
    })
    .catch(err => {
      nextLoading.value = false;
    });
};

const scrollToStart = (lineNumber: number) => {
  tableRef.value.scrollToRow(
    datasource.value.find(item => Number(item.line_number) === lineNumber),
  );
};

const handleCancel = () => {
  emit('toggle-modal');
};
</script>

<style>
.highlight {
  background-color: #ff0;
}
</style>
