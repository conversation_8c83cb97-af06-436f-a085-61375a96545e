<template>
  <a-select
    show-search
    option-filter-prop="label"
    :value="selectedValue"
    placeholder="请选择"
    allow-clear
    :options="options"
    :loading="loading"
    @change="handleChange"
  />
</template>
<script setup lang="ts">
import { getEnumConfigsApi } from '@/api/sfmanage';
import { SelectProps } from 'ant-design-vue';

const props = withDefaults(
  defineProps<{
    value: string | number | undefined;
    enableDefault?: boolean;
    category: string;
  }>(),
  {
    enableDefault: true,
  },
);
const loading = ref(false);
const selectedValue = ref(props.value);
const emit = defineEmits<{
  (e: 'update:value', value: string | number): void;
  (e: 'change', value: string | number): void;
}>();
// 处理选项变化
const handleChange = (value: string | number) => {
  emit('update:value', value);
  emit('change', value);
};
const options = ref<SelectProps['options']>([]);
const loadOptions = async () => {
  loading.value = true;
  await getEnumConfigsApi(props.category).then(data => {
    options.value = data.items.map(item => {
      return {
        label: item.label,
        value: item.value,
      };
    });
    const currentValueNotExists =
      props.value !== undefined && data.items.findIndex(item => item.value === props.value) === -1;
    if (currentValueNotExists) {
      selectedValue.value = undefined; // 重置选项
    }
    // 使用enum中默认选项
    if (props.enableDefault && options.value.length > 0) {
      const defaultItem = data.items.find(item => item.is_default);
      if (defaultItem !== undefined) {
        selectedValue.value = defaultItem.value;
      }
      if (selectedValue.value) {
        emit('update:value', selectedValue.value);
      }
    }
  });
  loading.value = false;
};

// 组件挂载时加载选项
onMounted(() => {
  loadOptions();
});

// 监听 props.value 的变化
watch(
  () => props.value,
  newValue => {
    selectedValue.value = newValue;
  },
);
watch(
  () => props.category,
  (newValue, oldValue) => {
    if (newValue === oldValue) return;
    loadOptions();
  },
);
</script>
