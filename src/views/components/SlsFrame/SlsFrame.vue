<template>
  <a-tabs v-model:activeKey="activeKey" tab-position="left">
    <a-tab-pane v-for="i in props.logstores" :key="i" :tab="`${i}`">
      <iframe title="SLS日志" :src="url" style="height: 1000px; width: 100%; border: 0"></iframe>
    </a-tab-pane>
  </a-tabs>
</template>
<script setup lang="ts">
import { ref } from 'vue';
const activeKey = ref('');

const props = defineProps<{
  ticket: string;
  project: string;
  logstores: string[];
  regionId: string;
  queryString?: string;
  startTime?: number;
  endTime?: number;
}>();

const url = computed(() => {
  let baseurl = `https://sls.console.aliyun.com/lognext/project/${props.project}/logsearch/${activeKey.value}?slsRegion=${props.regionId}&sls_ticket=${props.ticket}&hideTopbar=true&hideSidebar=true&ignoreTabLocalStorage=true&hiddenOverview=true&hiddenBack=true&hiddenChangeProject=true&isShare=true&readOnly=true&hiddenLogChart=true&hiddenLogReduce=true&hiddenEtl=true`;
  if (props.queryString) {
    baseurl += `&queryString=${props.queryString}&encode=base64`;
  }
  if (props.startTime && props.endTime) {
    baseurl += `&queryTimeType=99&startTime=${props.startTime}&endTime=${props.endTime}`;
  }
  return baseurl;
});

watch(
  () => props.project,
  () => {
    activeKey.value = props.logstores[0];
  },
);

onMounted(() => {
  // 默认选择第一个
  if (props.logstores.length > 0) {
    activeKey.value = props.logstores[0];
  }
});
</script>
