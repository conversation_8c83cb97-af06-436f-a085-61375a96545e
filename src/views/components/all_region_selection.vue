<template>
  <a-select
    show-search
    :value="selectedValue"
    placeholder="请选择"
    :options="options"
    :loading="loading"
    @change="handleChange"
  />
</template>
<script setup lang="ts">
import { getRegionApi } from '@/api/permission';
import { SelectProps } from 'ant-design-vue';

const props = defineProps<{
  value?: string | number;
}>();
const loading = ref(false);
const selectedValue = ref(props.value);
const emit = defineEmits<{
  (e: 'update:value', value: string | number): void;
  (e: 'change', value: string | number): void;
}>();
// 处理选项变化
const handleChange = (value: string | number) => {
  emit('update:value', value);
  emit('change', value);
};
const options = ref<SelectProps['options']>([]);
const loadOptions = async () => {
  loading.value = true;
  await getRegionApi({ name: '' }).then(data => {
    options.value = data.map(item => {
      return {
        label: item.name,
        value: item.name,
      };
    });
  });
  loading.value = false;
};

// 组件挂载时加载选项
onMounted(() => {
  loadOptions();
});

// 监听 props.value 的变化
watch(
  () => props.value,
  newValue => {
    selectedValue.value = newValue;
  },
);
</script>
