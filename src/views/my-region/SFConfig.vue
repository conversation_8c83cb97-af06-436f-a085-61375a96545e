<template>
  <a-row>
    <a-col :span="6">
      <a-card title="私服定时更新配置" style="walign-content: center">
        <template #extra><a href="#"></a></template>
        <a-form name="basic" :wrapper-col="{ span: 24 }" autocomplete="off">
          <a-form-item label="代码分支" name="codeBranch">
            <a-select v-model:value="modelRef.codeBranch">
              <a-select-option v-for="item in codeBranches" :value="item.value">
                <span>{{ item.label }}</span>
                <span style="float: right">{{ item.desc }}</span>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="数值分支" name="username">
            <a-select v-model:value="modelRef.dataBranch">
              <a-select-option v-for="item in dataBranches" :value="item.value">
                <span>{{ item.label }}</span>
                <span style="float: right">{{ item.desc }}</span>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="定时方式" name="timer_type">
            <a-radio-group v-model:value="modelRef.timerType">
              <a-radio-button value="clocked">每日定点</a-radio-button>
              <a-radio-button value="interval">周期</a-radio-button>
              <a-radio-button value="crontab">crontab</a-radio-button>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="更新时间" name="username">
            <template v-if="modelRef.timerType == 'clocked'">
              <a-tag color="success">
                <template #icon>
                  <sync-outlined :spin="true" />
                </template>
                每天
              </a-tag>
              <a-time-picker
                v-model:value="modelRef.clocked"
                value-format="HH:mm"
                format="H:mm"
                width="100%"
              />
            </template>
            <template v-if="modelRef.timerType == 'interval'">
              <a-select
                ref="select"
                v-model:value="modelRef.interval"
                style="width: 100%"
                @focus="focus"
                @change="handleChange"
              >
                <a-select-option value="10m">每次间隔10分钟</a-select-option>
                <a-select-option value="30m">每次间隔30分钟</a-select-option>
                <a-select-option value="1h">每次间隔1小时</a-select-option>
                <a-select-option value="3h">每次间隔3小时</a-select-option>
                <a-select-option value="6h">每次间隔6小时</a-select-option>
                <a-select-option value="12h">每次间隔12小时</a-select-option>
              </a-select>
            </template>
            <template v-if="modelRef.timerType == 'crontab'">
              <a-input v-model:value="modelRef.crontab"></a-input>
            </template>
          </a-form-item>
          <a-form-item label="是否开启" name="enable">
            <a-switch v-model:checked="modelRef.enableJob" size="small" />
          </a-form-item>
          <a-form-item label="是否@操作人" name="enable_at">
            <a-switch v-model:checked="modelRef.enableAt" size="small" />
          </a-form-item>
        </a-form>
        <a-form-item class="error-infos" :wrapper-col="{ span: 14, offset: 4 }">
          <a-button
            type="primary"
            :disabled="isActionDisabled('updateRegionConfig')"
            @click.prevent="updateRegionConfig"
          >
            保存
          </a-button>
          <a-button style="margin-left: 10px" @click="getRegionConfig">Reset</a-button>
        </a-form-item>
      </a-card>
    </a-col>
    <a-col :span="1">
      <a-space></a-space>
    </a-col>

    <a-col v-if="userState.getUser.is_admin" :span="8">
      <a-card>
        <template #title>
          <span>
            镜像服配置
            <a-tag color="orange">注意：仅管理员才能操作，请谨慎操作！</a-tag>
          </span>
          <!-- 可以在这里添加更多内容，如图标 -->
        </template>
        <a-space direction="vertical" style="width: 100%">
          <p>
            每当区服
            <a-tag color="cyan" size="large">{{ currentSF }}</a-tag>
            进行如下操作时：
          </p>
          <a-checkbox-group v-model:value="actions" style="width: 100%">
            <a-space direction="vertical">
              <a-checkbox value="region:updateSfBinAndData">一键更新私服</a-checkbox>
              <a-checkbox value="region:updateSf">更新服务器代码</a-checkbox>
              <a-checkbox value="region:updateSfData">私服更新数值到所选分支</a-checkbox>
              <a-checkbox value="region:ufight_updateSf">更新Ufight代码并重启</a-checkbox>
              <a-checkbox value="region:ufight_updateSfData">Ufight更新数值到所选分支</a-checkbox>
            </a-space>
          </a-checkbox-group>
          <a-divider />
          <p>
            同时操作如下区服
            <a-tag color="orange">定时任务不参与操作</a-tag>
            :
          </p>
          <a-select
            v-model:value="regionMirror"
            mode="multiple"
            show-search
            placeholder="请选择一个区服"
            style="width: 100%"
            :filter-option="filterOption"
            size="middle"
            :options="regionOptions"
          ></a-select>
          <a-switch
            v-model:checked="mirrorEnable"
            checked-children="开启"
            un-checked-children="禁用"
          />

          <a-button
            type="primary"
            style="float: right; margin-top: 20px"
            @click="updateRegionMirrorConfig"
          >
            保存
          </a-button>
        </a-space>
      </a-card>
    </a-col>
    <a-col :span="1">
      <a-space></a-space>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, toRefs } from 'vue';
import {
  getBranchVersionApi,
  getRegionConfigApi,
  updateRegionMirrorConfigApi,
  updateRegionScheduleConfigApi,
} from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import dayjs from 'dayjs';
// import { Form } from 'ant-design-vue';
import { SyncOutlined } from '@ant-design/icons-vue';
import { isActionDisabled } from '@/util/permission/action';
import { getUserRegionApi } from '@/api/permission';
import type { SelectProps } from 'ant-design-vue';
import type { RegionMirrorConfig, RegionStatus } from '@/api/model';

let sfState = useCurrentSFState(); // 获取sf pinia state
// const useForm = Form.useForm;

const codeBranches = ref<any>([]);
const dataBranches = ref<any>([]);

const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服
// const currentSFStatus = sfState.getCurrentSFStatus;
const currentSFStatus = computed(() => sfState.getCurrentSFStatus);

const actions = ref([]);
const regionOptions = ref<SelectProps['options']>([]);

const regionMirror = ref([]);
const mirrorEnable = ref(true);

const modelRef = reactive({
  codeBranch: '1.2_dev',
  dataBranch: '1.2_dev',
  clocked: dayjs('05:58', 'HH:mm'),
  enableJob: false,
  enableAt: true,
  timerType: 'clocked',
  interval: '6h',
  crontab: '0 0 * * *', // 每天凌晨12点执行
});

// const { resetFields } = useForm(modelRef);

// 组件被挂载时，执行该方法
onMounted(() => {
  if (sfState.currentValue) {
    initData();
    getAllRegionName();
  }
});

function initData() {
  // console.log('currentSF.value:', currentSF);
  getCodeBranches(); // 获取所选择的分支版本的详细信息
  getDataBranches();
  getRegionConfig();
  initMirror();
}

function initMirror() {
  console.log('initMirror currentSFStatus: ', currentSFStatus.value);

  if (currentSFStatus.value.mirror_config) {
    const mirrorConfig = JSON.parse(currentSFStatus.value.mirror_config) as RegionMirrorConfig;

    console.log('initMirror mirrorConfig: ', mirrorConfig);

    regionMirror.value = mirrorConfig.mirror_region;
    mirrorEnable.value = mirrorConfig.is_enable;
    actions.value = mirrorConfig.actions;
  }
}

function getCodeBranches() {
  getBranchVersionApi({ type: 'code', module_name: 'all' }).then(data => {
    codeBranches.value = [];
    for (let key in data) {
      codeBranches.value.push({ value: key, label: key, desc: data[key] });
    }
  });
}

// 数值分支处理逻辑
function getDataBranches() {
  getBranchVersionApi({ type: 'data', module_name: 'all' }).then(data => {
    dataBranches.value = [];
    for (let key in data) {
      dataBranches.value.push({ value: key, label: key, desc: data[key] });
    }
    dataBranches.value.push({ value: 'localDesignData', label: '本地网盘', desc: '本地网盘数值' });
  });
}

function getRegionConfig() {
  getRegionConfigApi({ region: currentSF }).then(data => {
    // console.log(data);
    if (data) {
      modelRef.codeBranch = data.code_branch;
      modelRef.dataBranch = data.data_branch;
      modelRef.enableJob = data.enable;
      modelRef.enableAt = data.enable_at;
      modelRef.timerType = data.timer_type;
      if (modelRef.timerType == 'interval') {
        modelRef.interval = data.cron;
      } else if (modelRef.timerType == 'crontab') {
        modelRef.crontab = data.cron;
      } else {
        modelRef.clocked = dayjs(data.cron, 'HH:mm');
      }
    }
  });
}

function updateRegionConfig() {
  let cron = '';
  if (modelRef.timerType == 'interval') {
    cron = modelRef.interval;
  } else if (modelRef.timerType == 'crontab') {
    cron = modelRef.crontab;
  } else {
    if (!(modelRef.clocked instanceof dayjs)) {
      modelRef.clocked = dayjs(modelRef.clocked, 'HH:mm');
    }
    cron = modelRef.clocked.format('HH:mm');
  }

  updateRegionScheduleConfigApi({
    region: currentSF,
    code_branch: modelRef.codeBranch,
    data_branch: modelRef.dataBranch,
    enable: modelRef.enableJob,
    enable_at: modelRef.enableAt,
    cron: cron,
    timer_type: modelRef.timerType,
  }).then(() => {
    // 添加实际逻辑或注释说明
    // console.log('Region configuration updated successfully.');
  });
}

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

function getAllRegionName() {
  getUserRegionApi().then(data => {
    data.items.forEach((r: string) => {
      regionOptions.value.push({ value: r, label: r });
    });
  });
  // console.log('regionOptions: ', regionOptions);
}

function updateRegionMirrorConfig() {
  updateRegionMirrorConfigApi({
    actions: actions.value,
    mirror_region: regionMirror.value,
    region: currentSF,
    is_enable: mirrorEnable.value,
  }).then(() => {
    // 添加实际逻辑或注释说明
    // console.log('Region mirror configuration updated successfully.');
    sfState.setCurrentSFStatus(currentSF);
  });
}
</script>
