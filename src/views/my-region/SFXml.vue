<template>
  <a-card title="xml配置" style="width: 80%; align-content: center" :loading="loading">
    <template #extra><a href="#"></a></template>
    <a-button
      v-if="isEditing"
      type="primary"
      style="margin-right: 10px; background-color: orange"
      :disabled="isActionDisabled('editXml')"
      @click="endEdit"
    >
      确认
    </a-button>
    <a-button
      v-else
      type="primary"
      style="margin-right: 10px"
      :disabled="isActionDisabled('editXml')"
      @click="startEdit"
    >
      编辑
    </a-button>
    <a-button
      danger
      :disabled="!allowCommit || isActionDisabled('editXml')"
      type="primary"
      style="margin-right: 10px"
      @click="updateXmlConfig"
    >
      提交
    </a-button>
    <codemirror
      v-model="xmlContent"
      placeholder="Code goes here..."
      :style="{ height: '500px', backgroundColor: 'lightgrey', marginTop: '10px' }"
      :autofocus="true"
      :indent-with-tab="true"
      :tab-size="2"
      :extensions="extensions"
      :disabled="!isEditing"
    />
  </a-card>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { getXmlConfigApi, updateXmlConfigApi } from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { Codemirror } from 'vue-codemirror';
import { yaml } from '@codemirror/lang-yaml';
import { message } from 'ant-design-vue';
import { isLocked, isActionDisabled } from '@/util/permission/action';

const extensions = [yaml()];

const xmlContent = ref<string>('');
const sfState = useCurrentSFState(); // 获取sf pinia state
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服

let loading = ref<boolean>(false);
let isEditing = ref<boolean>(false);
let allowCommit = ref<boolean>(false);

// 组件被挂载时，执行该方法
onMounted(() => {
  if (sfState.currentValue) {
    initData();
  }
});

function initData() {
  isEditing.value = false;
  allowCommit.value = false;
  getXmlConfig(); // 获取所选择区服的xml配置
}

function getXmlConfig() {
  getXmlConfigApi(currentSF || '').then(data => {
    xmlContent.value = data;
  });
}

function startEdit() {
  isEditing.value = true;
  allowCommit.value = false;
}

function endEdit() {
  isEditing.value = false;
  allowCommit.value = true;
}

async function updateXmlConfig() {
  loading.value = true;
  await updateXmlConfigApi({ region: currentSF || '', content: xmlContent.value })
    .then(msg => {
      message.info(msg);
    })
    .catch(msg => {
      message.error(msg);
    });
  loading.value = false;
  allowCommit.value = false;
}
</script>
