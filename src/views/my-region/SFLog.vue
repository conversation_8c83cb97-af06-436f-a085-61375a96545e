<template>
  <a-space direction="vertical" style="width: 100%">
    <a-form layout="inline" style="margin-bottom: 30px">
      <a-form-item label="模块">
        <a-select
          v-model:value="currentModule"
          style="width: 120px"
          :options="moduleData"
          default-active-first-option
          @change="initIframeSrc"
        ></a-select>
      </a-form-item>
      你还可以进更复杂的搜素,
      <a target="_blank" :href="kibana_url">全屏查看</a>
    </a-form>
    <iframe
      :src="kibana_url"
      height="1000px"
      width="100%"
      border="0px"
      frameborder="no"
      scrolling="no"
      allowtransparency="yes"
    />
  </a-space>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
// import dayjs, { Dayjs } from 'dayjs';
import { useCurrentSFState } from '@/stores/modules/sf';
import { getAllModuleApi } from '@/api/sfmanage';

let sfState = useCurrentSFState(); // 获取sf pinia state
const kibana_url = ref<string>();

let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服.
const moduleData = ref<any[]>([]);
const currentModule = ref<string>('gameserver');

onMounted(() => {
  // // console.log('dayjs().unix(): ', dayjs().unix());
  // // console.log('dayjs().valueOf(): ', dayjs().valueOf());
  if (currentSF) {
    initIframeSrc();
    getAllModule();
  }
});

function getAllModule() {
  const res = getAllModuleApi({ region: currentSF });
  res.then(data => {
    // // console.log('getAllModule', data);
    data.forEach((r: any) => {
      moduleData.value.push({ value: r, label: r });
    });
  });
}

function initIframeSrc() {
  // const from = dayjs().add(1, 'hour').valueOf();
  // const to = dayjs().valueOf();
  const from = 'now-15m';
  const to = 'now';
  const columns = 'TIME,LOG_LEVEL,FILE_NAME,FUNC_NAME,LINE_NUMBER,MSG';
  let hostname = 'nap-' + currentSF?.replace('_', '-') + '-cn-0001';
  // let src_url =
  //   'http://kibana-log.nap.mihoyo.com/app/discover#/?' +
  //   '_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:' +
  //   from +
  //   ',to:' +
  //   to +
  //   '))' +
  //   '&_a=(columns:!(TIME,LOG_LEVEL,FILE_NAME,FUNC_NAME,LINE_NUMBER,MSG),' +
  //   "filters:!(('$state':(store:appState)," +
  //   'meta:(alias:!n,disabled:!f,index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,' +
  //   "key:'__tag__:__hostname__',negate:!f,params:(query:" +
  //   hostname +
  //   '),type:phrase),' +
  //   "query:(match_phrase:('__tag__:__hostname__':" +
  //   hostname +
  //   ')))),' +
  //   'meta:(alias:!n,disabled:!f,index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,key:__topic__,negate:!f,params:(query:' +
  //   currentModule.value +
  //   '),type:phrase),query:(match_phrase:(__topic__:' +
  //   currentModule.value +
  //   ')))),' +
  //   'hideChart:!t,' +
  //   'index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,' +
  //   'interval:auto,' +
  //   "query:(language:kuery,query:'')," +
  //   "sort:!(!(TIME,desc)))";
  let sfSrcUrl = `http://kibana-log.ops.juequling.co/app/discover#/?_g=(filters:!(),query:(language:kuery,query:''),refreshInterval:(pause:!t,value:10000),time:(from:${from},to:${to}))&_a=(columns:!(${columns}),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,key:'__tag__:__hostname__',negate:!f,params:(query:${hostname}),type:phrase),query:(match_phrase:('__tag__:__hostname__':${hostname}))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,key:__topic__,negate:!f,params:(query:${currentModule.value}),type:phrase),query:(match_phrase:(__topic__:${currentModule.value})))),hideChart:!t,index:fc7b3ea0-486b-11ee-9c45-63d87c007cdf,interval:auto,query:(language:kuery,query:''),sort:!(!(TIME,desc)))`;
  if (currentSF == 'rel_beta_cn') {
    sfSrcUrl = `http://rel_beta_cn-log.ops.juequling.co/app/discover#/?_a=(columns:!(LOG_LEVEL,FILE_NAME,FUNC_NAME,LINE_NUMBER,MSG),filters:!(('$state':(store:appState),meta:(alias:!n,disabled:!f,index:e1a51e40-ee94-11ef-a2fd-bf0a93073db5,key:'__tag__:__hostname__',negate:!f,params:(query:nap-rel-beta-cn-0001),type:phrase),query:(match_phrase:('__tag__:__hostname__':nap-rel-beta-cn-0001))),('$state':(store:appState),meta:(alias:!n,disabled:!f,index:e1a51e40-ee94-11ef-a2fd-bf0a93073db5,key:__topic__,negate:!f,params:(query:gameserver),type:phrase),query:(match_phrase:(__topic__:gameserver)))),hideChart:!t,index:e1a51e40-ee94-11ef-a2fd-bf0a93073db5,interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',desc)))&_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))`;
  }
  kibana_url.value = sfSrcUrl;
}
</script>
