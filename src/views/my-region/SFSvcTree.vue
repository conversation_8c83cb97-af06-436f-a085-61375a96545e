<template>
  <div ref="container" style="width: 100%; height: 800px"></div>
</template>

<script lang="ts">
import { ref, onMounted } from 'vue';
import G6 from '@antv/g6';

export default {
  name: 'Graph',
  setup() {
    const container = ref(null);
    let graph = null;

    onMounted(() => {
      graph = new G6.Graph({
        container: container.value,
        // width: 1000,
        height: 800,
        modes: {
          default: ['drag-node', 'zoom-canvas'],
        },
        defaultNode: {
          size: [60, 30],
          style: {
            fill: '#8ac007',
            stroke: '#8ac007',
          },
        },
        defaultEdge: {
          style: {
            stroke: '#e2e2e2',
          },
        },
      });

      graph.data({
        nodes: [
          { id: 'node1', x: 100, y: 100, label: 'global dispatch' },
          { id: 'node2', x: 300, y: 100, label: 'dispatch' },
          { id: 'node3', x: 500, y: 100, label: 'gteserver' },
          { id: 'node4', x: 500, y: 200, label: 'gameserver' },
          { id: 'node5', x: 500, y: 300, label: 'nodeserver' },
          { id: 'node6', x: 700, y: 150, label: 'dbgate' },
          { id: 'node7', x: 700, y: 250, label: 'mailserver' },
          { id: 'node8', x: 700, y: 350, label: 'rankserver' },
          { id: 'node9', x: 700, y: 450, label: 'snsserver' },
        ],
        edges: [
          { source: 'node1', target: 'node2' },
          { source: 'node2', target: 'node3' },
          { source: 'node2', target: 'node4' },
          { source: 'node2', target: 'node5' },
          { source: 'node5', target: 'node6' },
          { source: 'node5', target: 'node7' },
          { source: 'node5', target: 'node8' },
          { source: 'node5', target: 'node9' },
        ],
      });

      graph.render();
    });

    return { container };
  },
};
</script>
