<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="服务器监控">
      <iframe
        :src="server_monitor_url"
        height="2020px"
        width="100%"
        border="0px"
        frameborder="no"
        scrolling="no"
        allowtransparency="yes"
      />
    </a-tab-pane>
    <a-tab-pane key="2" tab="数据库监控">
      <iframe
        :src="db_monitor_url"
        height="2020px"
        width="100%"
        border="0px"
        frameborder="no"
        scrolling="no"
        allowtransparency="yes"
      />
    </a-tab-pane>
    <a-tab-pane key="3" tab="上报指标监控">
      <iframe
        :src="indicator_monitor_url"
        height="2020px"
        width="100%"
        border="0px"
        frameborder="no"
        scrolling="no"
        allowtransparency="yes"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
// import dayjs, { Dayjs } from 'dayjs';
import { useCurrentSFState } from '@/stores/modules/sf';

let sfState = useCurrentSFState(); // 获取sf pinia state
const server_monitor_url = ref<string>();
const db_monitor_url = ref<string>();
const indicator_monitor_url = ref<string>();

let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服.

onMounted(() => {
  // // console.log('dayjs().unix(): ', dayjs().unix());
  // // console.log('dayjs().valueOf(): ', dayjs().valueOf());
  if (currentSF) {
    initServerMonitor();
    initIndicatorMonitor();
    initDbMonitor();
  }
});

function getSfNode() {
  if (currentSF?.startsWith('rel_')) {
    return 'rel';
  } else if (currentSF?.startsWith('dev_')) {
    return 'dev';
  } else if (currentSF?.startsWith('live_')) {
    return 'live';
  } else {
    return 'sf';
  }
}

/**
 * 如何展示导航栏：F12 进入开发者模式后，选择Elements，然后搜索 css-keyl2d ，删除 css-keyl2d 样式
 */
function initServerMonitor() {
  const baseUrl = 'https://sre-grafana.juequling.com/d/Bkl9bBYik/5Li75py655uR5o6n';

  const from = 'now-1h';
  const to = 'now';

  const params = {
    orgId: '1',
    'var-datasource': 'Prometheus',
    'var-node': getSfNode(),
    'var-region': currentSF?.replace('.', '') || '',
    'var-app_name': 'all',
    'var-Module': 'all',
    'var-maxmount': '/',
    from,
    to,
    theme: 'light',
  };

  const url = new URL(baseUrl);
  url.search = new URLSearchParams(params).toString();

  server_monitor_url.value = url.href;
}

function initDbMonitor() {
  const baseUrl = 'https://sre-grafana.juequling.com/d/n0RIjr3nz/mysql';
  const from = 'now-1h';
  const to = 'now';
  const params = {
    orgId: '1',
    'var-DataSource': 'Gaia-DataProxy',
    'var-region_id': 'ap-northeast-1',
    'var-clusterDesc': 'pc-6weu8bv1kvz20xc5e',
    'var-clusterId': 'pc-uf6r7401a1cev4364',
    'var-nodeId': 'pi-uf63678ofig3x4k66',
    refresh: '5s',
    from: from,
    to: to,
    theme: 'light',
  };

  const url = new URL(baseUrl);
  url.search = new URLSearchParams(params).toString();

  db_monitor_url.value = url.href;
}

function initIndicatorMonitor() {
  const baseUrl = 'https://sre-grafana.juequling.com/d/Vwtvf4w4zxzx/5pyN5Yqh5Zmo55uR5o6n5LiK5oql';
  const from = 'now-7d';
  const to = 'now';
  const params = {
    orgId: '1',
    'var-datasource': 'Gaia-DataProxy',
    'var-region': currentSF,
    'var-app_name': 'all',
    'var-metric_name': 'MONITOR_BRANCH_NAME',
    'var-type': 'p99',
    'var-threshold': '30000',
    'var-step': '30m',
    from: from,
    to: to,
    theme: 'light',
  };

  const url = new URL(baseUrl);
  url.search = new URLSearchParams(params).toString();

  indicator_monitor_url.value = url.href;
}
</script>
