<template>
  <div>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="日志查询">
        <a-alert type="info" closable>
          <template #message>默认只展示模块 {{ currentModule }} 三小时内全部日志</template>
        </a-alert>
        <a-divider></a-divider>
        <a-form layout="inline">
          <a-form-item label="模块">
            <a-select
              v-model:value="currentModule"
              style="width: 150px"
              :options="moduleData"
              default-active-first-option
              filterable
              show-search
              @change="getServerLog"
            ></a-select>
          </a-form-item>
          <template v-if="currentModule === 'gameserver'">
            <a-form-item label="日志文件">
              <a-select
                ref="select"
                v-model:value="gameLogFile"
                style="width: 187px"
                :options="logFileOptions"
                show-search
                @change="getServerLog"
              ></a-select>
            </a-form-item>
            <a-form-item v-if="gameLogFile === 'gameserver.log'" label="日志类型">
              <a-select
                ref="select"
                v-model:value="logType"
                style="width: 187px"
                :options="logTypeOptions"
                show-search
                @change="getServerLog"
              ></a-select>
            </a-form-item>
          </template>
          <template v-else>
            <a-form-item label="日志类型">
              <a-select
                ref="select"
                v-model:value="logType"
                style="width: 187px"
                :options="logTypeOptions"
                show-search
                @change="getServerLog"
              ></a-select>
            </a-form-item>
          </template>

          <a-form-item label="日志时间">
            <a-range-picker
              v-model:value="dateRange"
              show-time
              :ranges="{
          '三小时内': [dayjs().subtract(3, 'hour'), dayjs()] as RangeValue,
          '两小时内': [dayjs().subtract(2, 'hour'), dayjs()] as RangeValue,
          '一小时内': [dayjs().subtract(1, 'hour'), dayjs()] as RangeValue,
          '半小时内': [dayjs().subtract(30, 'minute'), dayjs()] as RangeValue,
        }"
              @change="handleDateChange"
            />
          </a-form-item>
          <a-form-item label="日志内容">
            <a-input-search
              v-model:value="logSearch"
              placeholder="请输入关键词"
              enter-button
              @search="getServerLog"
            ></a-input-search>
          </a-form-item>
          <a-button type="dashed" style="margin-left: auto" @click="openModal">
            拉取日志文件
          </a-button>
        </a-form>

        <a-modal
          v-model:visible="visible"
          :title="`拉取模块 ${currentModule} 日志`"
          @ok="fetchServerLogFile"
        >
          <a-form>
            <a-form-item label="拉取日志的开始时间">
              <a-date-picker
                v-model:value="fetchStartTime"
                show-time
                placeholder="选择时间"
                :show-now="false"
              >
                <template #renderExtraFooter>
                  <a-button
                    size="small"
                    type="link"
                    style="margin-right: 5px"
                    @click="handleDateSelect(3, 'hour')"
                  >
                    三小时前
                  </a-button>
                  <a-button
                    size="small"
                    type="link"
                    style="margin-right: 5px"
                    @click="handleDateSelect(2, 'hour')"
                  >
                    两小时前
                  </a-button>
                  <a-button
                    size="small"
                    type="link"
                    style="margin-right: 5px"
                    @click="handleDateSelect(1, 'hour')"
                  >
                    一小时前
                  </a-button>
                  <a-button size="small" type="link" @click="handleDateSelect(30, 'minute')">
                    半小时前
                  </a-button>
                  <a-button size="small" type="link" @click="handleDateSelect(5, 'minute')">
                    五分钟前
                  </a-button>
                </template>
              </a-date-picker>
            </a-form-item>
          </a-form>
        </a-modal>

        <div class="background" style="margin-top: 30px">
          <a-table
            :columns="tableColumns"
            :data-source="serverLogs"
            :pagination="{
              total: pageParam.total,
              pageSize: 100,
              pageSizeOptions: ['100'],
              onChange: (page: number, pageSize: number) => handleChange(page, pageSize),
              showTotal: (total: number) => `总共 ${total}条`,
            }"
            size="small"
            :scroll="{ y: 600 }"
            :loading="loading"
          >
            <template #bodyCell="{ column, text }">
              <template v-if="column.dataIndex === 'LOG_LEVEL'">
                <a-tag :color="fmtLogLevelTag(text)">{{ fmtLogLevel(text) }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="实时日志">
        <sf-game-real-time-log></sf-game-real-time-log>
      </a-tab-pane>
      <a-tab-pane key="3" tab="协议日志">
        <server-packet-log :region="currentSF || ''"></server-packet-log>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { fetchServerLogFileApi, getAllModuleApi, getServerLogApi } from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import type { ServerLogReq, ServerLogRes } from '@/api/model';
import { message, type SelectProps } from 'ant-design-vue';
// import { UserOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/modules/user';
// import { timePickerProps } from 'ant-design-vue/lib/time-picker/time-picker';
import dayjs, { Dayjs, type ManipulateType } from 'dayjs';
import SfGameRealTimeLog from '@/views/my-region/SFGameRealTimeLog.vue';
import ServerPacketLog from '@/views/my-region/components/ServerPacketLog.vue';

type RangeValue = [Dayjs, Dayjs];

// const containerRef = ref();
let sfState = useCurrentSFState(); // 获取sf pinia state
let userState = useUserStore();

const activeKey = ref('1');
const moduleData = ref<any[]>([]);
const currentModule = ref<string>('gameserver');
const loading = ref(false);
const visible = ref(false);
const pageParam = ref({
  page: 1,
  size: 100,
  total: 0,
});
const startTime = ref(0);
const endTime = ref(0);
const dateRange = ref<RangeValue>();
const fetchStartTime = ref<Dayjs>();

let serverLogs = ref<ServerLogRes[]>([]);
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服.
const logType = ref<string>('Error');
const logSearch = ref<string>('');
const gameLogFile = ref<string>('gameserver.log');
const logFileOptions = ref<SelectProps['options']>([
  {
    value: 'gameserver.log',
    label: 'gameserver.log',
  },
  {
    value: 'gameserver.anti.log',
    label: 'gameserver.anti.log',
  },
]);

const logTypeOptions = ref<SelectProps['options']>([
  {
    value: 'Debug',
    label: 'Debug 详细日志',
  },
  {
    value: 'Info',
    label: 'Info 信息日志',
  },
  {
    value: 'Warning',
    label: 'Warning 警告日志',
  },
  {
    value: 'Error',
    label: 'Error 错误日志',
  },
  {
    value: 'All',
    label: 'All 所有日志',
  },
]);

const tableColumns = computed(() => {
  if (gameLogFile.value !== 'gameserver.anti.log') {
    return columns;
  } else {
    return antiLogcolumns;
  }
});

const columns = [
  {
    title: '时间',
    dataIndex: 'TIME',
    width: 170,
  },
  {
    title: '日志级别',
    dataIndex: 'LOG_LEVEL',
    width: 80,
    align: 'center',
  },
  {
    title: '线程ID',
    dataIndex: 'THREAD_ID',
    width: 90,
  },
  {
    title: '文件名',
    dataIndex: 'FILE_NAME',
    width: 300,
  },
  {
    title: '函数名',
    dataIndex: 'FUNC_NAME',
    width: 150,
  },
  {
    title: '行数',
    dataIndex: 'LINE_NUMBER',
    width: 45,
  },
  {
    title: '日志内容',
    dataIndex: 'MSG',
  },
];

const antiLogcolumns = [
  {
    title: '时间',
    dataIndex: 'time',
    width: 170,
  },
  {
    title: '日志内容',
    dataIndex: 'content',
  },
];

onMounted(() => {
  if (currentSF) {
    getAllModule();
    getServerLog();
  }
});

function handleDateChange() {
  if (dateRange.value) {
    startTime.value = dateRange.value[0].unix();
    endTime.value = dateRange.value[1].unix();
  } else {
    startTime.value = 0;
    endTime.value = 0;
  }
  getServerLog();
}

function handleDateSelect(dateNum: number, dateUnit: ManipulateType) {
  fetchStartTime.value = dayjs().subtract(dateNum, dateUnit);
}

function fmtLogLevel(val: string) {
  if (!val) {
    return '';
  }
  return val.replace('LOG_', '');
}

function fmtLogLevelTag(val: string) {
  if (!val) {
    return '';
  }
  switch (val.replace('LOG_', '')) {
    case 'ERROR':
      return 'red';
    case 'DEBUG':
      return '';
    case 'INFO':
      return 'green';
    case 'WARNING':
      return 'orange';
    default:
      return '';
  }
}

function handleChange(page: number, pageSize: number) {
  pageParam.value.page = page;
  pageParam.value.size = pageSize;
  getServerLog();
}

function getServerLog() {
  if (currentSF) {
    loading.value = true;
    getServerLogsData(pageParam.value.page, pageParam.value.size).then(data => {
      serverLogs.value = data.items;
      pageParam.value.total = data.total;
      loading.value = false;
    });
  }
}

async function getServerLogsData(page: number, size: number) {
  let query: ServerLogReq = {
    region: currentSF || '',
    log_type: logType.value,
    module_name: currentModule.value,
    page: page,
    size: size,
    log_file: currentModule.value === 'gameserver' ? gameLogFile.value : '',
  };
  if (startTime.value) {
    query.start_time = startTime.value;
  }
  if (endTime.value) {
    query.end_time = endTime.value;
  }
  if (logSearch.value) {
    query.search = logSearch.value;
  }
  const data = await getServerLogApi(query);
  return data;
}

function openModal() {
  visible.value = true;
}

function fetchServerLogFile() {
  const startTime = fetchStartTime.value ? `${fetchStartTime.value.unix()}` : null;
  fetchServerLogFileApi({
    account: userState.getUSerName,
    region: currentSF,
    module_name: currentModule.value,
    fetch_start_time: startTime,
  }).then(() => {
    visible.value = false;
    fetchStartTime.value = undefined;
  });
}

const downloadFile = (data: any) => {
  // 创建 Blob 对象
  const blob = new Blob([data], { type: 'text/plain' });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;

  // 设置文件名
  const fileName = `log_${currentSF}_${currentModule.value}_${new Date().getTime()}.txt`;
  link.setAttribute('download', fileName);

  // 添加到文档并触发点击
  document.body.appendChild(link);
  link.click();

  // 清理
  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

const getSingleLog = (logfile: string, logs: ServerLogRes[]) => {
  if (logfile === 'gameserver.log') {
    return 
  }

}

// 处理下载请求
const handleDownload = async () => {
  let logdata: string[] = [];
  let total = 0;
  let page = 1;
  let size = 1000;
  const maxloop = 100;
  for (let i = 0; i < maxloop; i += size) {
    await getServerLogsData(page, size).then(data => {
      logdata.push(...data.items);
      total = data.total;
    });
    if (logdata.length >= total) {
      break;
    }
    page++;
  }

  try {
    downloadFile(logdata);
    message.success('日志下载成功');
  } catch (error) {
    message.error('下载失败，请稍后重试');
    console.error('下载错误:', error);
  }
};

function getAllModule() {
  const res = getAllModuleApi({ region: currentSF || '' });
  res.then(data => {
    // // console.log('getAllModule', data);
    data.forEach((r: any) => {
      moduleData.value.push({ value: r, label: r });
    });
  });
}
</script>
<style scoped>
#components-affix-demo-target.scrollable-container {
  height: 100%;
  overflow-y: scroll;
}

#components-affix-demo-target .background {
  height: 500px;
  color: #e8e8e8;
}
</style>
