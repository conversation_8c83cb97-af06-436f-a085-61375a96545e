<template>
  <div>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      :row-key="record => record.id"
      bordered
      @change="handleTableChange"
    >
      <template #expandColumnTitle>
        <span style="color: red">More</span>
      </template>
      <template #expandedRowRender="{ record }">
        <a-descriptions bordered size="small">
          <a-descriptions-item
            v-for="(value, key) in parseJson(record.task_params)"
            :key="key"
            :label="key"
          >
            <pre class="json-content">{{
                typeof value === 'object' ? JSON.stringify(value, null, 2) : value
              }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'task_status'">
          <a-tag v-if="record.task_status == 1" color="green">
            <template #icon>
              <check-circle-outlined />
            </template>
            已完成
          </a-tag>
          <a-tag v-else-if="record.task_status == 2" color="blue">
            <template #icon>
              <check-circle-outlined />
            </template>
            已创建
          </a-tag>
          <a-tag v-else-if="record.task_status == 3" color="orange">
            <template #icon>
              <minus-circle-outlined />
            </template>
            错误
          </a-tag>
          <a-tag v-else-if="record.task_status == 4" color="#2db7f5">
            <template #icon>
              <minus-circle-outlined />
            </template>
            未知状态
          </a-tag>
          <a-tag v-else-if="record.task_status == 5" color="purple">
            <template #icon>
              <minus-circle-outlined />
            </template>
            运行超时
          </a-tag>
          <a-tag v-else color="green">
            <template #icon>
              <check-circle-outlined />
            </template>
            已完成
          </a-tag>
        </template>
        <template v-else-if="column.key === 'action'">
          <span>
            <a @click="showDrawer(record)">查看详情</a>
            <!--          <a-divider type="vertical" />-->
            <!--          <a>Delete</a>-->
            <!--          <a-divider type="vertical" />-->
            <!--          <a class="ant-dropdown-link">-->
            <!--            More actions-->
            <!--            <down-outlined />-->
            <!--          </a>-->
          </span>
        </template>
      </template>
    </a-table>
    <template v-if="currentRow">
      <a-drawer
        v-model:visible="visible"
        width="30%"
        :title="currentRow.task_name + '【' + currentRow.id + '】'"
        placement="right"
      >
        <template v-for="(value, key) in parseJson(currentRow)" :key="key">
          <pre class="json-content">{{
              typeof value === 'object' ? JSON.stringify(value, null, 2) : value
            }}</pre>
        </template>
        <a-divider>执行的主机</a-divider>
        <pre>{{ formattedJson(currentRow.task_hosts) }}</pre>
        <a-divider>执行的参数</a-divider>
        <pre>{{ formattedJson(currentRow.task_params) }}</pre>
        <a
          style="float: right"
          :href="
            'https://ops-test.juequling.com/hotwheel/business/taskDetail?taskId=' +
            currentRow.task_id +
            '&step=2'
          "
          target="_blank"
        >
          管理员进入标准运维，查看执行详情
        </a>
      </a-drawer>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue';
import { getOperateLogApi } from '@/api/sfmanage';
import { useUserStore } from '@/stores/modules/user';
import { useCurrentSFState } from '@/stores/modules/sf';
import dayjs from 'dayjs';
import type { TableProps } from 'ant-design-vue';
import { usePagination } from 'vue-request';
import { CheckCircleOutlined, LoadingOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
import { languages } from 'monaco-editor';
import json = languages.json;

let userState = useUserStore();
let sfState = useCurrentSFState(); // 获取sf pinia state
// 定义双向绑定的变量
const currentSF = ref<any>(sfState.currentValue);
const total = ref<number>(0);
const visible = ref<boolean>(false);
const currentRow = ref();
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '操作',
    dataIndex: 'task_name',
    key: 'task_name',
  },
  {
    title: '任务ID',
    dataIndex: 'task_id',
    key: 'task_id',

    // customCell: (_, rowIndex) => {
    //   const span = rowSpanMap.value[rowIndex];
    //   return {
    //     rowSpan: span ?? 1,
    //   };
    // },
  },
  {
    title: '操作时间',
    dataIndex: 'operate_time',
    key: 'operate_time',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'), // 格式化操作时间
  },
  {
    title: '完成时间',
    dataIndex: 'finish_time',
    key: 'finish_time',
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'), // 格式化完成时间
  },
  {
    title: '任务状态',
    key: 'task_status',
    dataIndex: 'task_status',
  },
  {
    title: '操作人',
    key: 'operator',
    dataIndex: 'operator',
  },
  {
    title: 'Action',
    key: 'action',
  },
];

type APIParams = {
  size?: number;
  page?: number;
  region_name: string;
  [key: string]: any;
};

type APIResult = {
  data: {
    items: any[];
    total: number;
  };
};

const queryData = async (params: APIParams) => {
  params.region_name = currentSF.value;
  const res = await getOperateLogApi(params);
  total.value = res.total;
  return res.items;
};

const {
  data: dataSource,
  run,
  loading,
  current,
  pageSize,
} = usePagination(queryData, {
  pagination: {
    currentKey: 'page',
    pageSizeKey: 'size',
  },
});

// 创建一个映射来记录每个名字第一次出现的位置及其总数量

const pagination = computed(() => ({
  total: total.value, // 确保 total 是正确的
  current: current.value,
  pageSize: pageSize.value,
}));

const handleTableChange: TableProps['onChange'] = (
  pag: { pageSize: number; current: number },
  filters: any,
  sorter: any,
) => {
  run({
    size: pag.pageSize,
    page: pag.current,
    ...filters,
  });
};

// 生成合并映射（仅处理相邻行中值相同的合并）
// 👇 正确合并逻辑：仅合并“相邻的相同 name 值”
function getRowSpanMap(data, key) {
  const map = {};
  let start = 0;

  while (start < data.length) {
    const currentValue = data[start][key];
    let end = start + 1;

    // 向后查找相邻的相同项
    while (end < data.length && data[end][key] === currentValue) {
      end++;
    }

    const count = end - start;
    map[start] = count;
    for (let i = start + 1; i < end; i++) {
      map[i] = 0;
    }

    start = end;
  }

  return map;
}

const rowSpanMap = computed(() => getRowSpanMap(dataSource.value, 'task_id'));

const showDrawer = record => {
  currentRow.value = record;
  visible.value = true;
};

const formattedJson = (str: string) => {
  try {
    return JSON.stringify(JSON.parse(str), null, 4);
  } catch (e) {
    console.error('Invalid JSON:', str);
    return str;
  }
};

const parseJson = (str: string) => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return { raw: str };
  }
};
</script>

<style scoped></style>
