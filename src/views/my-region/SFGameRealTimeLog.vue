<template>
  <div>
    <a-form layout="inline">
      <a-form-item label="模块">
        <a-select
          v-model:value="currentModule"
          style="width: 120px"
          :options="moduleData"
          default-active-first-option
          filterable
          show-search
          @change="handleQueryChange"
        ></a-select>
      </a-form-item>
      <a-form-item label="日志类型">
        <a-select
          ref="select"
          v-model:value="logType"
          style="width: 187px"
          :options="logTypeOptions"
        ></a-select>
      </a-form-item>
      <a-form-item label="筛选关键字">
        <a-input
          :value="filterKeyword"
          placeholder="请输入关键字"
          style="width: 300px"
          @input="debouncedFilterKeyword($event.target.value)"
        ></a-input>
      </a-form-item>
      <a-form-item label="高亮关键字">
        <a-select
          v-model:value="highlights"
          mode="tags"
          style="width: 300px"
          placeholder="请输入关键字后，回车"
          :options="options"
        ></a-select>
      </a-form-item>
      <a-form-item>
        <a-button v-if="isLive" danger @click="closeRealTimeLog">关闭实时日志</a-button>
        <a-button v-else @click="openRealTimeLog">开启实时日志</a-button>
        <a-button v-if="enableScroll" danger style="margin-left: 5px" @click="enableScroll = false">
          关闭自动滚动
        </a-button>
        <a-button v-else style="margin-left: 5px" @click="enableScroll = true">
          开启自动滚动
        </a-button>
        <a-button style="margin-left: 5px" @click="logs = []">清屏</a-button>
        <!-- <a-spin size="small" :spinning="loading"></a-spin> -->
      </a-form-item>
    </a-form>

    <div class="background" style="margin-top: 30px">
      <div
        id="components-affix-demo-target"
        ref="containerRef"
        class="scrollable-container"
        style="height: 100%"
      >
        <div class="background" style="height: 780px">
          <MonacoEditor
            v-model="code"
            read-only
            width="100%"
            height="800px"
            :highlights="highlights"
            :enable-auto-scroll="enableScroll"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { getAllModuleApi } from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import MonacoEditor from '@/components/monaco/monaco.vue';
import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source';
import { SelectProps } from 'ant-design-vue';
import { debounce } from 'lodash';

class RetriableError extends Error {}
class FatalError extends Error {}

let sfState = useCurrentSFState(); // 获取sf pinia state
let userState = useUserStore();
const logTypeOptions = ref<SelectProps['options']>([
  {
    value: 'ALL',
    label: 'ALL 所有日志',
  },
  {
    value: 'DEBUG',
    label: 'DEBUG 详细日志',
  },
  {
    value: 'INFO',
    label: 'INFO 信息日志',
  },
  {
    value: 'WARNING',
    label: 'WARNING 警告日志',
  },
  {
    value: 'ERROR',
    label: 'ERROR 错误日志',
  },
]);
const logType = ref<string>('ALL');
const moduleData = ref<any[]>([]);
const currentModule = ref<string>('gameserver');
const token = userState.getToken;
const filterKeyword = ref<string>('');
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服.
// const code = ref<string>('');
const highlights = ref([]);
const options = ['aid', 'uid'].map((hl, _) => ({ value: hl }));
const maximum = 5000; // 设置最大行数
const logs = ref<string[]>([]);

// 延迟修改筛选关键词, 避免多次渲染monacoeditor导致的性能问题 
const debouncedFilterKeyword = debounce((value: string) => {
  filterKeyword.value = value;
}, 100);

const filteredLog = (log: string) => {
  let keywordMatch = false;
  if (filterKeyword.value === '') {
    keywordMatch = true;
  } else if (log.includes(filterKeyword.value)) {
    keywordMatch = true;
  }

  let levelMatch = false;
  if (logType.value === 'ALL') {
    levelMatch = true;
  } else if (log.startsWith(`LOG_${logType.value}`)) {
    levelMatch = true;
  }
  return keywordMatch && levelMatch;
};

const code = computed(() => {
  if (logs.value.length == 0) {
    return '';
  }

  const filteredLogs = [];
  let currentLog = logs.value[0];
  for (let i = 1; i < logs.value.length; i++) {
    if (logs.value[i].startsWith('LOG_')) {
      if (filteredLog(currentLog)) {
        filteredLogs.push(currentLog);
      }
      currentLog = logs.value[i];
    } else {
      currentLog += '\n' + logs.value[i];
    }
  }
  if (filteredLog(currentLog) && currentLog.length > 0) {
    filteredLogs.push(currentLog);
  }
  return filteredLogs.join('\n');
});

const currentController = ref<AbortController>(new AbortController());
const isLive = ref<boolean>(false);
const enableScroll = ref<boolean>(true);
onMounted(() => {
  if (currentSF) {
    getAllModule();
    closeRealTimeLog();
    openRealTimeLog();
  }
});
onUnmounted(() => {
  // console.log('onUnmounted closeRealTimeLog');
  closeRealTimeLog();
});

function closeRealTimeLog() {
  // console.log(currentController.value);
  currentController.value.abort();
  currentController.value = new AbortController();
  isLive.value = false;
}

function handleQueryChange() {
  closeRealTimeLog();
  logs.value = [];
  openRealTimeLog();
}

function openRealTimeLog() {
  if (!currentSF) {
    return;
  }
  isLive.value = true;
  fetchEventSource('/api/v1/region/log/stream', {
    openWhenHidden: true,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: token ?? '',
    },
    body: JSON.stringify({
      region: currentSF,
      module: currentModule.value,
    }),
    async onopen(response) {
      if (response.ok && response.headers.get('content-type') === EventStreamContentType) {
        return; // everything's good
      } else if (response.status >= 400 && response.status <= 500 && response.status !== 429) {
        // client-side errors are usually non-retriable:
        throw new FatalError();
      } else {
        throw new RetriableError();
      }
    },
    onmessage(msg) {
      // if the server emits an error message, throw an exception
      // so it gets handled by the onerror callback below:
      if (msg.event === 'message') {
        logs.value.push(msg.data);
        if (logs.value.length > maximum) {
          logs.value.shift();
        }
      }
    },
    onclose() {
      // if the server closes the connection unexpectedly, retry:
      // console.log('on close');
      throw new RetriableError();
    },
    onerror(err) {
      if (err instanceof FatalError) {
        currentController.value.abort();
        currentController.value = new AbortController();
        logs.value.push('无法获取实时日志，未安装日志agent或agent离线');
        throw err; // rethrow to stop the operation
      } else {
        // do nothing to automatically retry. You can also
        // return a specific retry interval here.
      }
    },
    signal: currentController.value.signal,
  });
}

function getAllModule() {
  const res = getAllModuleApi({ region: currentSF });
  res.then(data => {
    // // console.log('getAllModule', data);
    data.forEach((r: any) => {
      moduleData.value.push({ value: r, label: r });
    });
  });
}
</script>
<style scoped>
/* #components-affix-demo-target.scrollable-container {
  height: 100%;
  overflow-y: scroll;
}

#components-affix-demo-target .background {
  height: 500px;
  color: #e8e8e8;
} */
</style>
