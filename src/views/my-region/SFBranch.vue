<template>
  <page-container :title="`${route.meta.title} `">
    <template #content>
      <a-table :columns="columns" :data-source="dataSource">
        <template #title>
          <div>
            <a-button type="primary" @click="openAddModal = true">新建</a-button>
            <a-modal v-model:open="openAddModal" title="创建一个新的分支" @ok="handleOk">
              <a-form
                :model="formState"
                name="basic"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18 }"
                autocomplete="off"
                style="margin-top: 50px"
              >
                <a-form-item label="分支名字" name="name" v-bind="validateInfos.name">
                  <a-input v-model:value="formState.name" />
                </a-form-item>
                <a-form-item label="描述" name="desc" v-bind="validateInfos.desc">
                  <a-input v-model:value="formState.desc" />
                </a-form-item>
                <a-form-item label="模块名字" name="module_name" v-bind="validateInfos.module_name">
                  <a-radio-group v-model:value="formState.module_name">
                    <a-radio-button value="all">all</a-radio-button>
                    <a-radio-button value="ufightserver">ufightserver</a-radio-button>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="分支类型" name="type" v-bind="validateInfos.type">
                  <a-radio-group v-model:value="formState.type">
                    <a-radio-button value="code">code</a-radio-button>
                    <a-radio-button value="data">data</a-radio-button>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="是否启用" name="enable" v-bind="validateInfos.enable">
                  <a-switch v-model:checked="formState.enable" />
                </a-form-item>
              </a-form>
            </a-modal>
            <a-button type="primary" style="float: right" @click="listAllBranch">刷新</a-button>
          </div>
        </template>
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <a-input
              ref="searchInput"
              :placeholder="`Search ${column.dataIndex}`"
              :value="selectedKeys[0]"
              style="width: 188px; margin-bottom: 8px; display: block"
              @change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
              @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
            />
            <a-button
              type="primary"
              size="small"
              style="width: 90px; margin-right: 8px"
              @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              Search
            </a-button>
            <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
              Reset
            </a-button>
          </div>
        </template>
        <template #customFilterIcon="{ filtered }">
          <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'name' || column.key === 'desc'">
            <span v-if="branchState.searchText && branchState.searchedColumn === record.dataIndex">
              <template
                v-for="(fragment, i) in column
                  .toString()
                  .split(
                    new RegExp(`(?<=${branchState.searchText})|(?=${branchState.searchText})`, 'i'),
                  )"
              >
                <mark
                  v-if="fragment.toLowerCase() === branchState.searchText.toLowerCase()"
                  :key="i"
                  class="highlight"
                >
                  {{ fragment }}
                </mark>
                <template v-else>{{ fragment }}</template>
              </template>
            </span>
          </template>
          <template v-else-if="column.key === 'type'">
            <span>
              <a-tag :color="record.type === 'code' ? 'green' : 'geekblue'">
                {{ record.type }}
              </a-tag>
            </span>
          </template>
          <template v-if="column.key === 'enable'">
            <a-switch v-model:checked="record.enable" @change="updateBranchVersion(record)" />
          </template>
          <template v-else-if="column.key === 'action'">
            <span></span>
            <a-popconfirm
              title="Are you sure delete this branch?"
              ok-text="Yes"
              cancel-text="No"
              @confirm="deleteBranchVersion(record)"
            >
              <a href="#">Delete</a>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </template>
  </page-container>
</template>
<script lang="ts" setup>
import { SearchOutlined } from '@ant-design/icons-vue';
import { useRoute } from 'vue-router';
import {
  createBranchVersionApi,
  deleteBranchVersionApi,
  getBranchVersionApi,
  updateBranchVersionApi,
} from '@/api/sfmanage';
import type { TableProps } from 'ant-design-vue';
import { reactive, ref, onMounted } from 'vue';
import { Form } from 'ant-design-vue';

const useForm = Form.useForm;
const route = useRoute();
const searchInput = ref();
const openAddModal = ref<boolean>(false);

interface FormState {
  name: string;
  desc: string;
  module_name: string;
  type: string;
  enable: boolean;
}

const formState = reactive<FormState>({
  name: '',
  desc: '',
  module_name: 'all',
  type: 'code',
  enable: true,
});

const branchState = reactive({
  searchText: '',
  searchedColumn: '',
});

const columns: TableProps['columns'] = [
  {
    title: '分支名字',
    dataIndex: 'name',
    key: 'name',
    customFilterDropdown: true,
    onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: visible => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc',
    customFilterDropdown: true,
    onFilter: (value, record) => record.desc.toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: visible => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },
  },
  {
    title: '模块名字',
    dataIndex: 'module_name',
    key: 'module_name',
    filters: [
      { text: 'all', value: 'all' },
      { text: 'ufightserver', value: 'ufightserver' },
    ],
    onFilter: (value, record) => record.module_name === value,
  },
  {
    title: '分支类型',
    key: 'type',
    dataIndex: 'type',
    filters: [
      { text: 'code', value: 'code' },
      { text: 'data', value: 'data' },
    ],
    onFilter: (value, record) => record.type === value,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
  },
  {
    title: '是否启用',
    key: 'enable',
  },
  {
    title: 'Action',
    key: 'action',
  },
];
const dataSource = ref([]);
const listAllBranch = async () => {
  dataSource.value = await getBranchVersionApi({ type: 'all' });
  // console.log('dataSource: ', dataSource.value);
};

onMounted(async () => {
  await listAllBranch();
});

const handleSearch = (selectedKeys, confirm, dataIndex) => {
  confirm();
  branchState.searchText = selectedKeys[0];
  branchState.searchedColumn = dataIndex;
};

const handleReset = clearFilters => {
  clearFilters({ confirm: true });
  branchState.searchText = '';
};

// :rules="[{ required: true, message: '请输入名字!' }]"

const rulesRef = reactive({
  name: [
    {
      required: true,
      message: '请输入一个分支名字,如 2.0_live 、 3.0_dev',
    },
  ],
  desc: [
    {
      required: true,
      message: '请输入一个描述，或者直接使用分支名字',
    },
  ],
  module_name: [
    {
      required: true,
      message: '请选择一个模块名字',
    },
  ],
  type: [
    {
      required: true,
      message: '请选择应用分支的类型',
    },
  ],
});
const { resetFields, validate, validateInfos } = useForm(formState, rulesRef);
const handleOk = () => {
  validate()
    .then(() => {
      // console.log('------------------->', toRaw(formState));

      createBranchVersionApi(toRaw(formState)).then(data => {
        openAddModal.value = false;
        listAllBranch();
      });
    })
    .catch(err => {
      // console.log('error', err);
    });
};

const deleteBranchVersion = record => {
  deleteBranchVersionApi(record.key).then(data => {
    // console.log(data);
    listAllBranch();
  });
};

const updateBranchVersion = record => {
  updateBranchVersionApi({ id: record.id, enable: record.enable }).then(data => {
    // console.log(data);
    listAllBranch();
  });
};
</script>
<style scoped>
.highlight {
  background-color: rgb(255, 192, 105);
  padding: 0px;
}
</style>
