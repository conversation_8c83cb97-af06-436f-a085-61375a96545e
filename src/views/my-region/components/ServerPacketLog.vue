<template>
  <div>
    <a-form
      :model="searchForm"
      name="horizontal_login"
      autocomplete="off"
      @finish="searchRegionLog"
    >
      <a-row :gutter="24">
        <a-col :span="5">
          <a-form-item
            label="日志时间"
            name="dateRange"
            :rules="[{ validator: validateDateRange, trigger: 'change' }]"
            required
          >
            <a-range-picker
              v-model:value="searchForm.dateRange"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              :show-time="{
                format: 'HH:mm:ss',
                defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
              }"
              @change="handleDateRangeChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="协议日志" name="log_file" :rules="[{ required: true }]">
            <a-select v-model:value="searchForm.log_file" style="width: 100%">
              <a-select-option value="gameserver.packet.log">gameserver.packet.log</a-select-option>
              <a-select-option value="rankserver.packet.log">rankserver.packet.log</a-select-option>
              <a-select-option value="matchserver.packet.log">
                matchserver.packet.log
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col>
          <a-form-item>
            <a-button type="primary" html-type="submit" shape="round" :loading="taskLoading">
              <template #icon>
                <CaretRightOutlined />
              </template>
              开始采集
            </a-button>
            <a-button
              :disabled="!taskId || !taskLoading"
              type="danger"
              style="margin-left: 5px"
              shape="round"
              :loading="cancelLoading"
              @click="handleCancel(taskId)"
            >
              <template #icon>
                <StopOutlined />
              </template>
              停止采集
            </a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template v-if="taskId && !taskLoading">
      <ServerLogTable
        :region="props.region"
        :task-id="taskId"
        :table-height="580"
        :view-context="false"
        :hide-title="true"
      ></ServerLogTable>
    </template>
    <template v-else>
      <a-empty style="height: 780px; padding: 350px 0">
        <template #description>
          <span>填写表单开始采集任务</span>
        </template>
      </a-empty>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { CaretRightOutlined, StopOutlined } from '@ant-design/icons-vue';
import { cancelTaskApi, collectTaskApi, getTaskDetailApi } from '@/api/log';
import { message } from 'ant-design-vue';
import ServerLogTable from '@/views/components/ServerLog/ServerLogTable.vue';
import dayjs, { Dayjs } from 'dayjs';
import { TaskDetailRes } from '@/api/model';
import { Rule } from 'ant-design-vue/es/form';

const props = defineProps<{
  region: string;
}>();

type RangeValue = [Dayjs, Dayjs];

interface SearchForm {
  dateRange?: RangeValue;
  log_file: string;
}
const searchForm = reactive<SearchForm>({
  log_file: 'gameserver.packet.log',
});

const taskLoading = ref<boolean>(false);
const cancelLoading = ref<boolean>(false);
const taskRes = ref<TaskDetailRes>();
const searchRegionLog = async () => {
  taskId.value = '';
  if (!searchForm.log_file || !searchForm.dateRange) {
    return;
  }
  taskLoading.value = true;
  var from = searchForm.dateRange[0];
  var to = searchForm.dateRange[1];

  await collectTaskApi({
    region: props.region,
    from: from.format('YYYY-MM-DD HH:mm:ss'),
    to: to.format('YYYY-MM-DD HH:mm:ss'),
    module: searchForm.log_file.split('.')[0],
    log_file: searchForm.log_file,
    search_field: 'other',
  })
    .then(data => {
      taskId.value = data.task_id;
      waitForTaskFinish(data.task_id)
        .then((result: TaskDetailRes) => {
          taskLoading.value = false;
          taskRes.value = result;
          // console.log(result);
        })
        .catch(error => {
          taskLoading.value = false;
          message.error('任务失败: ', error);
        });
    })
    .catch(() => {
      taskLoading.value = false;
    });
};

const waitForTaskFinish = (
  taskId: string,
  checkInterval = 500,
  timeout = 60000,
): Promise<TaskDetailRes> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    async function checkStatus() {
      if (!props.region) {
        return;
      }
      try {
        await getTaskDetailApi(props.region, taskId).then(data => {
          // 检查任务状态
          if (data.status === 2) {
            resolve(data); // 任务完成
          } else if (data.status === 0 || data.status === 1) {
            // 未完成，继续轮询
            setTimeout(checkStatus, checkInterval);
          } else if (Date.now() - startTime >= timeout) {
            reject(new Error('Polling timed out')); // 超时
          } else {
            reject(new Error('Task failed')); // 任务失败
          }
        });
      } catch (error) {
        reject(error); // 返回错误
      }
    }

    checkStatus(); // 开始轮询
  });
};

const validateDateRange = async (_rule: Rule, value: RangeValue) => {
  if (!value) {
    return Promise.reject('请选择时间范围');
  }
  // console.log(value[0], value[1], value[0].diff(value[1], 'second'));
  if (value[1].diff(value[0], 'second') > 3600) {
    return Promise.reject('开始时间和结束时间差距不能超过1小时');
  } else if (value[1].diff(value[0], 'second') < 60) {
    return Promise.reject('开始时间和结束时间差距不能小于一分钟');
  }

  return Promise.resolve();
};

const handleDateRangeChange = (val: RangeValue) => {
  searchForm.dateRange = [val[0].second(0), val[1].second(0)];
};

const taskId = ref<string>('');

const handleCancel = async (cancelId: string) => {
  if (!props.region) {
    return;
  }
  cancelLoading.value = true;
  await cancelTaskApi(props.region, cancelId).then(() => {
    taskId.value = '';
    cancelLoading.value = false;
    return;
  });
};
</script>
