<template>
  <div>
    <a-button key="deleteSfUser" type="dashed" :disabled="props.disabled" @click="showModal">
      Patch更新
    </a-button>
    <a-modal
      v-model:open="open"
      :title="'你正在进行区服: ' + props.region + ' 的 Patch 部署'"
      width="40%"
      @ok="handleOk"
    >
      <a-divider>当前服务器patch</a-divider>
      <a-table :data-source="dataSource" :columns="columns">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'update_time'">
            {{ dayjs.utc(record.update_time).local().format('YYYY-MM-DD HH:mm:ss') }}
          </template>
          <template v-if="column.key === 'patch_time'">
            {{ dayjs.utc(record.patch_time).local().format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </template>
      </a-table>
      <a-divider>请选择一个patch id 进行更新</a-divider>
      <a-row>
        <a-tree-select
          v-model:value="patchId"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '600px', overflow: 'auto', 'margin-top': '20px' }"
          placeholder="请输入patch id 进行搜索..."
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          tree-node-filter-prop="label"
          :pagination="false"
        >
          <template #title="{ value: val, label }">
            <b v-if="val === 'parent 1-1'" style="color: #08c">sss</b>
            <template v-else>{{ label }}</template>
          </template>
        </a-tree-select>
      </a-row>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { getAllPatchApi, updateSfPatchApi } from '@/api/sfmanage';
import { useUserStore } from '@/stores/modules/user';
import type { TreeSelectProps } from 'ant-design-vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
const open = ref<boolean>(false);
const userState = useUserStore();
const props = defineProps({
  region: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: '',
  },
});
const columns = [
  {
    title: '模块',
    dataIndex: 'module',
    key: 'module',
  },
  {
    title: 'PATCH ID',
    dataIndex: 'patch_id',
    key: 'patch_id',
  },
  {
    title: 'patch构建时间',
    dataIndex: 'patch_time',
    key: 'patch_time',
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    key: 'update_time',
  },
];
const dataSource: Ref<[]> = ref([]);

const patchId = ref<string>();
const treeData = ref<TreeSelectProps['treeData']>([]);
const showModal = () => {
  open.value = true;
  listAllPatch();
};

const handleOk = async (e: MouseEvent) => {
  // console.log(e);
  open.value = false;
  await updateSfPatchApi({
    account: userState.getUSerName,
    region: props.region,
    patch_id: patchId.value,
  });
};

// 获取所有Patch信息
function listAllPatch() {
  getAllPatchApi(props.region).then(data => {
    // console.log(data);
    // 示例 UTC 时间
    dataSource.value = data.current_patch;
    for (let patch of data.all_patch) {
      // console.log('patch-------------> ', patch);

      let children = [];

      for (let patchInfo of patch.patch_infos) {
        const utcTime = dayjs.utc(patchInfo.update_time);

        // 转换为本地时间
        const localTime = utcTime.local();

        children.push({
          label: patchInfo.patch_id + ' - ' + localTime.format('YYYY-MM-DD HH:mm:ss'), // 输出本地时间
          value: patchInfo.patch_id,
        });
      }
      treeData.value.push({
        label: patch.module,
        value: patch.module,
        children: children,
      });
    }
    // console.log(treeData);
  });
}

watch(patchId, () => {
  // console.log(patchId.value);
});
</script>
