<template>
  <a-button
    key="deleteSfUser"
    type="dashed"
    danger
    :disabled="isLocked() || isActionDisabled('deleteSfUser')"
    @click="showDeleteModal"
  >
    清空私服用户数据
  </a-button>

  <a-modal v-model:visible="deleteUserVisible" title="清空私服用户数据">
    <a-form style="margin-top: 10px">
      <a-radio-group v-model:value="deleteType" style="margin-top: 10px">
        <a-radio-button v-for="i in allDeleteType" :key="i.value" :value="i.value">
          {{ i.label }}
        </a-radio-button>
      </a-radio-group>
      <div v-if="deleteType === 'partialUid'" style="margin-top: 10px">
        <a-form-item v-for="(u, i) in formState.uid" :key="i" label="UID">
          <a-input-number :key="i" v-model:value="formState.uid[i]" style="width: 60%" />
          <a-button
            type="primary"
            shape="circle"
            style="margin-left: 10px"
            :icon="h(PlusOutlined)"
            @click="addNewUid"
          />
          <a-button
            type="primary"
            danger
            shape="circle"
            style="margin-left: 10px"
            :icon="h(MinusOutlined)"
            @click="delNewUid(i)"
          />
        </a-form-item>
      </div>
      <a-form-item label="清空redis">
        <a-switch
          v-model:checked="formState.flush_redis"
          checked-children="是"
          un-checked-children="否"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button key="back" @click="handleCancel">取消</a-button>
      <a-button key="confirm" type="primary" @click="deleteSfUserData">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { isActionDisabled, isLocked } from '@/util/permission/action';
import { h, createVNode } from 'vue';
import { deleteSfUserApi } from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { PlusOutlined, MinusOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { Modal, message } from 'ant-design-vue';

interface FormState {
  account: string;
  region: string;
  uid: string[];
  flush_redis: boolean;
}

const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
const currentSF = sfState.currentValue || ''; // 获取当前下拉框 选择的私服
const allDeleteType = [
  { value: 'partialUid', label: '清空指定UID' },
  { value: 'allUid', label: '清空所有用户' },
];
let deleteType = ref<string>(allDeleteType[0].value);
let deleteUserVisible = ref<boolean>(false);
let formState = ref<FormState>({
  account: userState.getUSerName,
  region: currentSF,
  uid: [''],
  flush_redis: false,
});

function showDeleteModal() {
  deleteUserVisible.value = true;
}

function addNewUid() {
  formState.value.uid.push('');
}

function delNewUid(index: number) {
  if (formState.value.uid.length === 1) {
    message.warn('最少保留1条记录');
  } else {
    formState.value.uid.splice(index, 1);
  }
}

function handleCancel() {
  deleteUserVisible.value = false;
}

async function deleteSfUserData() {
  // console.log('deleteSfUser');
  // console.log(formState.value.uid);

  let content = `此操作将永久删除该私服【${sfState.currentValue}】下【所有】用户数据, 是否继续?`;
  let uid = 'all';
  if (deleteType.value === 'partialUid') {
    content = `此操作将永久删除该私服【${sfState.currentValue}】下用户数据, 是否继续?`;
    uid = formState.value.uid.join(',');
  }

  Modal.confirm({
    title: '重要提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: content,
    onOk() {
      deleteSfUserApi({
        region: sfState.currentValue,
        account: userState.getUSerName,
        uid: uid,
        flush_redis: formState.value.flush_redis,
      })
        .then(msg => {
          message.info(msg);
        })
        .catch(msg => {
          message.error(msg);
        });
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {
      // console.log('onCancel');
    },
  });

  deleteUserVisible.value = false;
}
</script>

<style scoped></style>
