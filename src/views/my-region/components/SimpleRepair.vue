<template>
  <div>
    <a-button
      type="dashed"
      danger
      :disabled="isLocked() || isActionDisabled('simpleRepair')"
      @click="showModal"
    >
      一键修复私服
    </a-button>
    <a-modal v-model:visible="modalVisible" :after-close="closeModal">
      <template #title>
        <ExclamationCircleOutlined style="color: red" />
        一键修复私服
      </template>

      <a-form
        ref="formRef"
        :model="form"
        :rules="{ checklist: [{ required: true, message: '请勾选修复项' }] }"
      >
        <a-form-item label="" prop="checklist">
          <a-checkbox-group v-model:value="form.checklist" style="padding: 20px" @change="onChange">
            <a-col
              v-for="option in checkOptions"
              :key="option.value"
              :span="8"
              style="padding: 5px"
            >
              <a-checkbox :value="option.value" :checked="option.default_checked">
                {{ option.label }}
              </a-checkbox>
            </a-col>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
      <div v-if="totalNeedStart">
        <a-alert
          message='操作执行完成后，你需要点击 “启服" ，完成游戏启动'
          type="warning"
          show-icon
        />
      </div>

      <div v-if="isCleanDiskChecked">
        <p show-icon style="color: red">
          注意：清理磁盘操作会引起游戏中用户掉线，并退回到登录界面!
        </p>
        <div style="margin-top: 20px">
          清理磁盘方式:
          <a-select v-model:value="disk_clean_type">
            <a-select-option value="compress">压缩</a-select-option>
            <a-select-option value="clean">清理</a-select-option>
          </a-select>
        </div>
      </div>

      <template #footer>
        <a-button key="back" @click="closeModal">取消</a-button>
        <a-button key="confirm" type="primary" @click="submit()">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts">
// import { createVNode } from 'vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { isLocked, isActionDisabled } from '@/util/permission/action';
import { repairSfApi } from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';

const userState = useUserStore();
const sfState = useCurrentSFState();

export default {
  name: 'SimpleRepair',
  components: {
    ExclamationCircleOutlined,
  },
  props: {},
  data() {
    return {
      // clean_disk：清理磁盘
      // clean_activity_data：清理活动数值
      // clean_download_ext：清理区服返回的下载信息，包括cdn_conf_ext和data_conf_ext
      // clean_pre_download_ext：清理区服返回的预下载信息pre_download_conf_ext
      checkOptions: [
        {
          label: '清理磁盘',
          value: 'clean_disk',
          desc: '该操作会引起游戏中用户掉线，并退回到登录界面\n操作执行完成后，你需要点击 "启服" ，完成游戏启动',
          need_start: true,
          default_checked: true,
        },
        {
          label: '清理活动数值',
          value: 'clean_activity_data',
          desc: '清理活动数值',
          need_start: false,
          default_checked: true,
        },
        {
          label: '清理下载信息',
          value: 'clean_download_ext',
          desc: '清理区服返回的下载信息，包括cdn_conf_ext和data_conf_ext',
          need_start: false,
          default_checked: true,
        },
        {
          label: '清理预下载信息',
          value: 'clean_pre_download_ext',
          desc: '清理区服返回的预下载信息pre_download_conf_ext',
          need_start: false,
          default_checked: true,
        },
        {
          label: '重置区服状态',
          value: 'clean_script_lock',
          desc: '将区服置为停止状态，并清理服务器上的脚本锁',
          need_start: false,
          default_checked: true,
        },
      ],
      form: {
        checklist: ['clean_script_lock'],
      },
      modalVisible: false,
      disk_clean_type: 'clean',
      totalNeedStart: false,
      isCleanDiskChecked: false,
    };
  },
  methods: {
    isLocked,
    isActionDisabled,
    showModal() {
      this.modalVisible = true;
    },
    closeModal() {
      this.modalVisible = false;
    },
    submit() {
      this.$refs.formRef.validate().then(() => {
        repairSfApi({
          account: userState.getUSerName,
          region: sfState.currentValue ?? '',
          repair_list: this.form.checklist,
          disk_clean_type: this.disk_clean_type,
        });
        this.closeModal();
      });
    },
    onChange(checkedList: string[]) {
      this.totalNeedStart = false;
      this.checkOptions.forEach(e => {
        if (checkedList.indexOf(e.value) !== -1 && e.need_start) {
          this.totalNeedStart = true;
        }
      });

      this.isCleanDiskChecked = checkedList.indexOf('clean_disk') !== -1;
    },
  },
};
</script>
