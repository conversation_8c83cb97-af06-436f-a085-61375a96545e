<template>
  <div>
    <a-button
      type="default"
      :disabled="isLocked() || isActionDisabled('modifyServerTime')"
      @click="showModifyServerTime"
    >
      修改服务器时间
    </a-button>
    <a-modal
      v-model:visible="modifyServerTimeVisible"
      style="width: 560px"
      title="服务器时间设置"
      :after-close="closeClock"
    >
      <a-space direction="vertical">
        <p>
          <a-space>
            当前私服时间：{{ currentSfTime }}
            <a-tag v-if="timeDelta < 0" color="red">
              比实际时间
              <b>早</b>
              {{ dateMsg }}
            </a-tag>
            <a-tag v-else-if="timeDelta > 0" color="red">
              比实际时间
              <b>晚</b>
              {{ dateMsg }}
            </a-tag>
            <a-tag v-else color="green">
              {{ dateMsg }}
            </a-tag>
          </a-space>
        </p>
        <p>
          调整
          <b>日期</b>
          <a-tooltip>
            <template #title>
              调整日期的时区为私服服务器的时区, eu服为东一区, us服为西五区, 其他服均为东八区
            </template>
            <question-circle-outlined style="margin-left: 5px" />
          </a-tooltip>
          至：
          <a-date-picker v-model:value="updateDate" :ranges="ranges">
            <template #renderExtraFooter>
              <a-button type="link" @click="changeDatePicker(-7)">一周前</a-button>
              <a-button type="link" @click="changeDatePicker(-3)">三天前</a-button>
              <a-button type="link" @click="changeDatePicker(-1)">昨天</a-button>
            </template>
          </a-date-picker>
        </p>
        <p>
          调整
          <b>时间</b>
          <a-tooltip>
            <template #title>
              调整时间的时区为私服服务器的时区, eu服为东一区, us服为西五区, 其他服均为东八区
            </template>
            <question-circle-outlined style="margin-left: 5px" />
          </a-tooltip>
          至：
          <a-time-picker v-model:value="updateTime"></a-time-picker>
        </p>
      </a-space>
      <template #footer>
        <a-button type="primary" style="float: left" @click="modifyServerTimeOffset('reset')">
          恢复至正常时间
        </a-button>
        <a-button key="back" @click="handleCancel">取消</a-button>
        <a-button key="confirm" type="primary" @click="modifyServerTimeOffset('modify')">
          确定
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeUnmount, reactive, ref } from 'vue';
// 修改私服时间
import { getServerTimeOffsetApi, modifyServerTimeOffsetApi } from '@/api/sfmanage';
import dayjs, { Dayjs } from 'dayjs';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { isLocked, isActionDisabled } from '@/util/permission/action';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
dayjs.extend(utc);
dayjs.extend(timezone);

const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服

const ranges = reactive({
  今天: [dayjs(), dayjs()],
  昨天: [dayjs(), dayjs().endOf('month')],
});
const modifyServerTimeVisible = ref<boolean>(false);
const currentSfTime = ref<string>('');
const updateTime = ref<Dayjs>(dayjs());
const updateDate = ref<Dayjs>(dayjs());
const dateMsg = ref<string>('与实际时间一致');
const timeDelta = ref<number>(0);

let timeId = 0;

onBeforeUnmount(() => {
  // 订阅私服 下拉框选择 发生变化，要记得改变当前的 私服名字
  closeClock();
});

function showModifyServerTime() {
  modifyServerTimeVisible.value = true;

  getServerTimeOffsetApi({ region: currentSF }).then(data => {
    timeDelta.value = data.offset;
    if (data.offset != 0) {
      dateMsg.value = getDeltaTime(data.offset);
    } else {
      dateMsg.value = '与实际时间一致';
    }
    setCurrentSfTime(data.current_time, data.offset);

    //初始化定时器
    const timeFun = setInterval(increaseCurrentSfTime, 1000);
    timeId = timeFun;
    // console.log('set setCurrentSfTime interval: ', timeId);
  });
}

function changeDatePicker(days: number) {
  updateDate.value = dayjs().add(days, 'day');
}

function increaseCurrentSfTime() {
  setCurrentSfTime(dayjs(currentSfTime.value).unix(), 1);
}

function setCurrentSfTime(current_time: number, second: number) {
  currentSfTime.value = dayjs
    .unix(current_time)
    .tz('Asia/Shanghai')
    .add(second, 'second')
    .format('YYYY/MM/DD HH:mm:ss Z');
  if (sfState.currentSF.endsWith('_us')) {
    // 美服采用西五区
    currentSfTime.value = dayjs
      .unix(current_time)
      .tz('Etc/GMT+5')
      .add(second, 'second')
      .format('YYYY/MM/DD HH:mm:ss Z');
  } else if (sfState.currentSF.endsWith('_eu')) {
    // 欧服采用东一区
    currentSfTime.value = dayjs
      .unix(current_time)
      .tz('Etc/GMT-1')
      .add(second, 'second')
      .format('YYYY/MM/DD HH:mm:ss Z');
  }
}

function modifyServerTimeOffset(operation: string) {
  const time =
    dayjs(updateDate.value).format('YYYY/MM/DD') + ' ' + dayjs(updateTime.value).format('HH:mm:ss');
  modifyServerTimeOffsetApi({
    account: userState.getUSerName,
    region: currentSF,
    operation: operation,
    time: time,
  });
  modifyServerTimeVisible.value = false;
}

const handleCancel = () => {
  modifyServerTimeVisible.value = false;
};

function closeClock() {
  console.log('clean setCurrentSfTime interval: ', timeId);
  clearInterval(timeId);
}

function getDeltaTime(offset: number) {
  const deltaAbs = Math.abs(offset);

  const deviation = dayjs.duration(deltaAbs * 1000);
  let day = deviation.days();
  let hour = deviation.hours();
  let minute = deviation.minutes();
  let second = deviation.seconds();
  let month = deviation.months();
  let year = deviation.years();
  let days = deviation.asDays();
  // 如果超过30天，将month和year转化为day
  if (month > 0 || year > 0) {
    day = Math.floor(days);
  }

  let d = day < 10 ? '0' + day : day;
  let h = hour < 10 ? '0' + hour : hour;
  let m = minute < 10 ? '0' + minute : minute;
  let s = second < 10 ? '0' + second : second;

  if (deltaAbs < 60) {
    return `${s}秒`;
  } else if (deltaAbs < 60 * 60) {
    return `${m}分钟${s}秒`;
  } else if (deltaAbs < 60 * 60 * 24) {
    return `${h}小时${m}分钟${s}秒`;
  } else {
    return `${d}天${h}小时${m}分钟${s}秒`;
  }
}
</script>
