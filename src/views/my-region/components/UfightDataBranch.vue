<template>
  <a-col :span="6">
    <a-form name="basic" autocomplete="off" style="width: 95%">
      <a-form-item label="Ufight数值分支" name="username">
        <a-select v-model:value="dataBranch" style="width: 90%" @change="handleDataBranchChange">
          <a-select-option v-for="item in dataBranches" :value="item.value">
            <span>{{ item.label }}</span>
            <span style="float: right">{{ item.desc }}</span>
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <a-card v-if="dataBranchInfo" key="ufightDataBranchInfo" title="Ufight最新数值版本信息">
      <template v-for="item in dataBranchInfo" :key="'data-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">{{ item.value }}</a-card-grid>
      </template>
    </a-card>
  </a-col>

  <a-col :span="6">
    <a-form>
      <a-form-item style="width: 90%" center>
        <a-button
          v-if="dataBranch == 'localDesignData'"
          type="primary"
          :disabled="isLocked() || isActionDisabled('ufight_updateSf')"
          @click="reloadSfData"
        >
          私服加载Ufight本地数值
        </a-button>
        <a-button
          v-else
          type="primary"
          :disabled="isLocked() || isActionDisabled('ufight_updateSf')"
          @click="updateSfData"
        >
          Ufight更新数值到所选分支
        </a-button>
      </a-form-item>
    </a-form>
    <a-card
      v-if="currentUfightDataBranchInfo"
      key="currentUfightDataBranchInfo"
      title="当前Ufight数值版本信息"
    >
      <template v-for="(item, index) in currentUfightDataBranchInfo" :key="'sfdata-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">
          <b
            :style="dataBranchInfo && item.value != dataBranchInfo[index].value ? 'color:red' : ''"
          >
            {{ item.value }}
          </b>
        </a-card-grid>
      </template>
    </a-card>
  </a-col>
</template>
<script lang="ts" setup>
// 组件被挂载时，执行该方法
import { onBeforeUnmount, onMounted, Ref, ref } from 'vue';
import {
  getBranchVersionApi,
  getCurrentUfightDataBranchInfoApi,
  getDataBranchApi,
  getUfigntDataBranchInfoApi,
  reloadSfDataApi,
  updateSfDataApi,
} from '@/api/sfmanage';
import { BrachInfoRes, RegionStatus } from '@/api/model';
import { SelectProps } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { isLocked, isActionDisabled } from '@/util/permission/action';

const route = useRoute();
const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服
const currentSFStatus = ref<RegionStatus>(sfState.getCurrentSFStatus);

const currentUfightDataBranchInfo = ref<BrachInfoRes[]>();
const dataBranches = ref<any>([]);
const dataBranch = ref<string>('1.2_dev');
const dataBranchInfo = ref<BrachInfoRes[]>();

function initData() {
  // console.log('currentSF.value:', currentSF);
  //数值分支
  getDataBranchInfo();
  getCurrentDataBranchInfo();
  getDataBranchs();
}

onMounted(() => {
  if (sfState.currentValue) {
    initData();
  }
});

// 数值分支处理逻辑
function getDataBranchs() {
  getBranchVersionApi({ type: 'data', module_name: 'ufightserver' }).then(data => {
    dataBranches.value = [];
    for (let key in data) {
      dataBranches.value.push({ value: key, label: key, desc: data[key] });
    }
    dataBranches.value.push({ value: 'localDesignData', label: '本地网盘', desc: '本地网盘数值' });
  });
}

function getCurrentDataBranchInfo() {
  // const { currentSFProp } = props;
  getCurrentUfightDataBranchInfoApi({ region: sfState.currentValue }).then(data => {
    currentUfightDataBranchInfo.value = data;
    for (let row of data) {
      if (row['key'] === 'stream') {
        dataBranch.value = row['value'];
        handleDataBranchChange();
      }
    }
  });
}

function getDataBranchInfo() {
  getUfigntDataBranchInfoApi({ branch: dataBranch.value, region: sfState.currentValue }).then(
    data => {
      dataBranchInfo.value = data;
    },
  );
}

function handleDataBranchChange() {
  getDataBranchInfo();
}

// 更新数值
function updateSfData() {
  updateSfDataApi({
    account: userState.getUSerName,
    region: currentSF,
    data_branch: dataBranch.value,
    module_name: 'ufightserver',
  });
}

function reloadSfData() {
  reloadSfDataApi({
    account: userState.getUSerName,
    region: currentSF,
    module_name: 'ufightserver',
  });
}

//暴露出去给父组件使用
export interface DataBranchExp {
  dataBranch: Ref<string>;
}

defineExpose<DataBranchExp>({
  dataBranch: dataBranch,
});
</script>
