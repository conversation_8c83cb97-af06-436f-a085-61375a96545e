<template>
  <a-col :span="6">
    <a-form name="basic" autocomplete="off" style="width: 90%">
      <a-form-item label="数值分支" name="username">
        <a-select
          v-model:value="dataBranch"
          :disabled="disableSel"
          style="width: 90%"
          @change="handleDataBranchChange"
        >
          <a-select-option v-for="item in dataBranches" :value="item.value">
            <span>{{ item.label }}</span>
            <span style="float: right">{{ item.desc }}</span>
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <a-card v-if="dataBranchInfo" key="dataBranchInfo" title="最新数值版本信息">
      <template v-for="item in dataBranchInfo" :key="'sfData-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">{{ item.value }}</a-card-grid>
      </template>
    </a-card>
  </a-col>

  <a-col :span="6">
    <a-form layout="inline" style="margin-bottom: 24px">
      <a-form-item style="width: 90%" center>
        <a-button
          type="primary"
          :disabled="isLocked() || isActionDisabled('updateSfData')"
          :loading="updateSfDataLoading"
          @click="dataBranch === 'localDesignData' ? reloadSfData() : updateSfData()"
        >
          {{ dataBranch === 'localDesignData' ? '私服加载本地数值' : '私服更新数值到所选分支' }}
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-checkbox v-model:checked="uploadOperationZip">上传operation.zip</a-checkbox>
      </a-form-item>
    </a-form>
    <a-card v-if="currentDataBreachInfo" key="currentDataBranchInfo" title="当前私服数值版本信息">
      <template v-for="(item, index) in currentDataBreachInfo" :key="'sfDataBranch-' + item.value">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">
          <b
            :style="dataBranchInfo && item.value != dataBranchInfo[index].value ? 'color:red' : ''"
          >
            {{ item.value }}
          </b>
        </a-card-grid>
      </template>
      <a-card-grid style="width: 30%; text-align: center">
        校验
        <a-tooltip>
          <template #title>通过muip校验数值</template>
          <QuestionCircleOutlined />
        </a-tooltip>
      </a-card-grid>
      <a-card-grid style="width: 70%; text-align: center">
        <a-tag v-if="currentDataBreachcheckInfo.status === 'success'" color="success">通过</a-tag>
        <a-tag v-else-if="currentDataBreachcheckInfo.status === 'fail'" color="error">未通过</a-tag>
        <a-tag v-else color="default">不可用</a-tag>
        <a-popover v-if="currentDataBreachcheckInfo.err_msg" trigger="click">
          <template #content>
            <span style="white-space: pre-wrap">{{ currentDataBreachcheckInfo.err_msg }}</span>
          </template>
          <a-button size="small" type="link">查看错误</a-button>
        </a-popover>
      </a-card-grid>
    </a-card>
  </a-col>
</template>
<script lang="ts" setup>
// 组件被挂载时，执行该方法
import { onMounted, Ref, ref, computed } from 'vue';
import {
  getBranchVersionApi,
  getCurrentDataBranchInfoApi,
  getDataBranchInfoApi,
  reloadSfDataApi,
  updateSfDataApi,
  getCurrentDataBranchCheckInfoApi,
} from '@/api/sfmanage';
import { BrachInfoRes, RegionStatus, DataCheckInfo } from '@/api/model';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { isLocked, isActionDisabled } from '@/util/permission/action';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { checkMirrorConfig } from '@/util/common';

const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服
const currentSFStatus = computed(() => sfState.getCurrentSFStatus); // 建立响应式依赖

const currentDataBreachInfo = ref<BrachInfoRes[]>();
const dataBranches = ref<any>([]);
const dataBranch = ref<string>();
const dataBranchInfo = ref<BrachInfoRes[]>();

const currentDataBreachcheckInfo = ref<DataCheckInfo>({ status: 'unknown', err_msg: '' });

const disableSel = ref<boolean>(false);
const updateSfDataLoading = ref<boolean>(false);
const uploadOperationZip = ref<boolean>(false);

// 下面分支和区服都已废弃，这个function可以去掉
function initDataBranch() {
  if ('dev_1.0_cn' == currentSF) {
    disableSel.value = true;
    dataBranch.value = '1.0_dev';
  } else if ('dev_release' == currentSF) {
    disableSel.value = true;
    dataBranch.value = 'weekly';
  } else if ('dev_0.3_cn' == currentSF) {
    disableSel.value = true;
    dataBranch.value = 'cb3_rel';
  } else if ('rel_0.3_cn' == currentSF) {
    disableSel.value = true;
    dataBranch.value = 'cb3_rel';
  } else {
    disableSel.value = false;
  }
}

function getStorageDataBranchKey() {
  return currentSF + '_dataBranch';
}

async function initData() {
  // 获取全部数值分支选项
  await getDataBranchs();

  // 获取当前数值分支详细信息
  await getCurrentDataBranchInfo();
  // 区服修改后，若用户之前没有选择过该区服的分支，则默认使用该区服的当前分支
  const storageDataBranch = localStorage.getItem(getStorageDataBranchKey());
  var defaultBranch =
    currentDataBreachInfo.value?.find(item => item.key === 'stream')?.value ?? '1.2_dev';
  if (defaultBranch === '本地网盘') {
    defaultBranch = 'localDesignData';
  }
  dataBranch.value = storageDataBranch ?? defaultBranch;

  // 获取选择的数值分支详细信息
  await getDataBranchInfo();
}

onMounted(async () => {
  if (sfState.currentValue) {
    initDataBranch();
    await initData();
  }
});

// 数值分支处理逻辑
async function getDataBranchs() {
  await getBranchVersionApi({ type: 'data', module_name: 'all' }).then(data => {
    dataBranches.value = [];
    for (let key in data) {
      dataBranches.value.push({ value: key, label: key, desc: data[key] });
    }
    dataBranches.value.push({ value: 'localDesignData', label: '本地网盘', desc: '本地网盘数值' });
  });
}

async function getCurrentDataBranchInfo() {
  await getCurrentDataBranchInfoApi({ region: sfState.currentValue }).then(data => {
    currentDataBreachInfo.value = data;
  });
  await getCurrentDataBranchCheckInfoApi({ region: sfState.currentValue }).then(data => {
    currentDataBreachcheckInfo.value = data;
  });
}

async function getDataBranchInfo() {
  await getDataBranchInfoApi({ branch: dataBranch.value, region: sfState.currentValue }).then(
    data => {
      dataBranchInfo.value = data;
      if ('localDesignData' == dataBranch.value && data == undefined) {
        // 针对选择本地分支的 数值分支 进行特殊处理
        dataBranchInfo.value = [
          { key: 'stream', value: 'no data' },
          { key: 'stream', value: 'no data' },
          { key: 'built', value: 'no data' },
        ];
      }
    },
  );
}

async function handleDataBranchChange() {
  // 更改数值分支后，存入localstorage
  localStorage.setItem(getStorageDataBranchKey(), dataBranch.value);
  await getDataBranchInfo();
}

// 更新数值
async function updateSfData() {
  updateSfDataLoading.value = true;
  let actionName = `【私服更新数值到所选分支 ${dataBranch.value}】`;
  const confirmed = await checkMirrorConfig(
    currentSFStatus.value,
    'region:updateSfData',
    actionName,
  );
  if (!confirmed) {
    updateSfDataLoading.value = false;
    return;
  }

  const dataRevisionObj = dataBranchInfo.value?.find(d => d.key === 'revision');
  const dataRevision = dataRevisionObj ? dataRevisionObj.value : '';

  await updateSfDataApi({
    account: userState.getUSerName,
    region: currentSF,
    data_branch: dataBranch.value,
    upload_operation_zip: uploadOperationZip.value,
    data_revision: dataRevision,
  }).catch(error => {
    // console.log(error);
  });
  updateSfDataLoading.value = false;
}

async function reloadSfData() {
  updateSfDataLoading.value = true;
  await reloadSfDataApi({
    account: userState.getUSerName,
    region: currentSF,
  }).catch(error => {
    // console.log(error);
  });
  updateSfDataLoading.value = false;
}

//暴露出去给父组件使用
export interface DataBranchExp {
  dataBranch: Ref<string>;
}

defineExpose<DataBranchExp>({
  dataBranch: dataBranch,
  dataBranchInfo: dataBranchInfo,
  uploadOperationZip: uploadOperationZip,
});
</script>
