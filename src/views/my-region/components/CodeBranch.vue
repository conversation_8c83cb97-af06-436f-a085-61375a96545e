<template>
  <a-col :span="6">
    <a-form name="basic" autocomplete="off" style="width: 90%">
      <a-form-item label="代码分支" name="codeBranch">
        <a-select
          v-model:value="codeBranch"
          style="width: 90%"
          :disabled="disableSel"
          @change="handleCodeBranchChange"
        >
          <a-select-option v-for="item in codeBranches" :value="item.value">
            <span>{{ item.label }}</span>
            <span style="float: right">{{ item.desc }}</span>
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <a-card v-if="codeBranchInfo" key="codeBranchInfo" title="最新代码版本信息">
      <template v-for="item in codeBranchInfo" :key="'code-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">{{ item.value }}</a-card-grid>
      </template>
    </a-card>
  </a-col>
  <a-col :span="6">
    <a-form>
      <a-form-item style="width: 90%">
        <a-button
          type="primary"
          :disabled="isLocked() || isActionDisabled('updateSf')"
          :loading="updateSfLoading"
          @click="updateSf"
        >
          更新服务器代码并重启
        </a-button>
      </a-form-item>
    </a-form>
    <a-card v-if="currentCodeBranchInfo" key="currentCodeBranchInfo" title="当前私服代码版本信息">
      <template v-for="(item, index) in currentCodeBranchInfo" :key="'sfcode-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">
          <b
            :style="codeBranchInfo && item.value != codeBranchInfo[index].value ? 'color:red' : ''"
          >
            {{ item.value }}
          </b>
        </a-card-grid>
      </template>
    </a-card>
  </a-col>
</template>
<script lang="ts" setup>
import { computed, onMounted, Ref, ref } from 'vue';
import { BrachInfoRes, RegionStatus } from '@/api/model';
import { isLocked, isActionDisabled } from '@/util/permission/action';
import {
  getBranchVersionApi,
  getCodeBranchInfoApi,
  getCurrentCodeBranchInfoApi,
  updateSfApi,
} from '@/api/sfmanage';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { checkMirrorConfig } from '@/util/common';

const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服
const currentSFStatus = computed(() => sfState.getCurrentSFStatus); // 建立响应式依赖

const currentCodeBranchInfo = ref<BrachInfoRes[]>();
const codeBranches = ref<any>([]);
const codeBranch = ref<string>();
const codeBranchInfo = ref<BrachInfoRes[]>();

const disableSel = ref<boolean>(false);
const updateSfLoading = ref<boolean>(false);

// 下面分支和区服都已废弃，这个function可以去掉
function initCodeBranch() {
  if ('dev_1.0_cn' == currentSF) {
    disableSel.value = true;
    codeBranch.value = '1.0_dev';
  } else if ('dev_release' == currentSF) {
    disableSel.value = true;
    codeBranch.value = 'release_week';
  } else if ('dev_0.3_cn' == currentSF) {
    disableSel.value = true;
    codeBranch.value = 'cb3_rel';
  } else if ('rel_0.3_cn' == currentSF) {
    disableSel.value = true;
    codeBranch.value = 'cb3_rel';
  } else {
    disableSel.value = false;
  }
}

function getStorageBranchKey() {
  return currentSF + '_codeBranch';
}

// 组件被挂载时，执行该方法
onMounted(async () => {
  if (sfState.currentValue) {
    initCodeBranch();
    await initData();
  }
});

async function initData() {
  // 先获取全部的分支选项
  await getCodeBranchs();
  // 获取当前私服的分支信息，即为部署在服务器上的分支版本详细信息
  await getCurrentCodeBranch();
  // 区服修改后，若用户之前没有选择过该区服的分支，则默认使用该区服的当前分支
  const defaultBranch =
    currentCodeBranchInfo.value?.find(item => item.key === 'branch')?.value ?? '1.2_dev';
  const storageCodeBranch = localStorage.getItem(getStorageBranchKey());
  codeBranch.value = storageCodeBranch ?? defaultBranch;
  // 获取当前选择的代码分支最新信息
  await getCodeBranchInfo();
}

// 代码分支处理逻辑
async function getCodeBranchs() {
  await getBranchVersionApi({ type: 'code', module_name: 'all' }).then(data => {
    codeBranches.value = [];
    for (let key in data) {
      if (!codeBranch.value) {
        codeBranch.value = key;
      }
      codeBranches.value.push({ value: key, label: key, desc: data[key] });
    }
  });
}

// 获取当前选择私服的 代码分支
async function getCurrentCodeBranch() {
  // const { currentSFProp } = props;
  await getCurrentCodeBranchInfoApi({ region: sfState.currentValue }).then(data => {
    currentCodeBranchInfo.value = data;
  });
}

async function getCodeBranchInfo() {
  await getCodeBranchInfoApi({ branch: codeBranch.value, region: sfState.currentValue }).then(
    data => {
      codeBranchInfo.value = data;
    },
  );
}

async function handleCodeBranchChange() {
  localStorage.setItem(getStorageBranchKey(), codeBranch.value);
  await getCodeBranchInfo();
}

// 更新服务器代码
async function updateSf() {
  updateSfLoading.value = true;
  let actionName = `【更新服务器代码并重启 ${codeBranch.value}】`;
  const confirmed = await checkMirrorConfig(currentSFStatus.value, 'region:updateSf', actionName);
  if (!confirmed) {
    updateSfLoading.value = false;
    return;
  }
  console.log('--------------------start update code--------------------------');

  await updateSfApi({
    account: userState.getUSerName,
    region: currentSF,
    branch: codeBranch.value,
  }).catch(error => {
    console.log(error);
  });
  updateSfLoading.value = false;
}

//暴露出去给父组件使用
export interface CodeBranchExp {
  codeBranch: Ref<string>;
}

defineExpose<CodeBranchExp>({
  codeBranch: codeBranch,
});
</script>
