<template>
  <a-col :span="6">
    <a-form name="basic" autocomplete="off" style="width: 95%">
      <a-form-item label="Ufight代码分支" name="codeBranch">
        <a-select v-model:value="codeBranch" style="width: 90%" @change="handleCodeBranchChange">
          <a-select-option v-for="item in codeBranches" :value="item.value">
            <span>{{ item.label }}</span>
            <span style="float: right">{{ item.desc }}</span>
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <a-card v-if="codeBranchInfo" key="ufightCodeBranchInfo" title="Ufight最新代码版本信息">
      <template v-for="item in codeBranchInfo" :key="'code-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">{{ item.value }}</a-card-grid>
      </template>
    </a-card>
  </a-col>
  <a-col :span="6">
    <a-form>
      <a-form-item style="width: 90%">
        <a-button
          type="primary"
          :disabled="isLocked() || isActionDisabled('ufight_updateSf')"
          @click="updateSf"
        >
          更新Ufight代码并重启
        </a-button>
      </a-form-item>
    </a-form>
    <a-card
      v-if="currentCodeBreachInfo"
      key="currentUfightCodeBreachInfo"
      title="当前Ufight代码版本信息"
    >
      <template v-for="(item, index) in currentCodeBreachInfo" :key="'sfcode-' + item.key">
        <a-card-grid style="width: 30%; text-align: center">{{ item.key }}</a-card-grid>
        <a-card-grid style="width: 70%; text-align: center">
          <b
            :style="
              codeBranchInfo && codeBranchInfo[index] && item.value != codeBranchInfo[index].value
                ? 'color:red'
                : ''
            "
          >
            {{ item.value }}
          </b>
        </a-card-grid>
      </template>
    </a-card>
  </a-col>
</template>
<script lang="ts" setup>
import { onMounted, Ref, ref } from 'vue';
import { BrachInfoRes, RegionStatus } from '@/api/model';
import { SelectProps } from 'ant-design-vue';
import {
  getBranchVersionApi,
  // getCodeBranchApi,
  // getCodeBranchInfoApi,
  getCurrentCodeBranchInfoApi,
  getCurrentUfightCodeBranchInfoApi,
  getUfightCodeBranchInfoApi,
  updateSfApi,
} from '@/api/sfmanage';
import { useRoute } from 'vue-router';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import { isLocked, isActionDisabled } from '@/util/permission/action';

const route = useRoute();
const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服

const branchInfo: BrachInfoRes = {
  key: 'key',
  index: '0',
  value: 'value',
};
const currentCodeBreachInfo = ref<BrachInfoRes[]>([branchInfo]);
const codeBranches = ref<any>([]);
const codeBranch = ref<string>();
const codeBranchInfo = ref<BrachInfoRes[]>();

// 组件被挂载时，执行该方法
onMounted(() => {
  if (sfState.currentValue) {
    initData();
  }
});

function initData() {
  // console.log('currentSF.value:', currentSF);
  getCodeBranchInfo(); // 获取代码所有的分支
  getCurrentCodeBranch(); // 获取当前私服的分支信息，即为部署在服务器上的分支版本详细信息
  getCodeBranchs(); // 获取所选择的分支版本的详细信息
  //数值分支
}

// 代码分支处理逻辑
function getCodeBranchs() {
  getBranchVersionApi({ type: 'code', module_name: 'ufightserver' }).then(data => {
    codeBranches.value = [];
    for (let key in data) {
      codeBranches.value.push({ value: key, label: key, desc: data[key] });
    }
  });
}

// 获取当前选择私服的 代码分支
function getCurrentCodeBranch() {
  // const { currentSFProp } = props;
  getCurrentUfightCodeBranchInfoApi({ region: sfState.currentValue }).then(data => {
    // console.log('naty------------------>', data);
    currentCodeBreachInfo.value = data;
    for (let row of data) {
      // debugger;
      if (row['key'] === 'branch') {
        codeBranch.value = row['value'];
        handleCodeBranchChange();
      }
    }
  });
}

function getCodeBranchInfo() {
  getUfightCodeBranchInfoApi({ branch: codeBranch.value, region: sfState.currentValue }).then(
    data => {
      codeBranchInfo.value = data;
    },
  );
}

function handleCodeBranchChange() {
  getCodeBranchInfo();
}

// 更新服务器代码
function updateSf() {
  updateSfApi({
    account: userState.getUSerName,
    region: currentSF,
    branch: codeBranch.value,
    module_name: 'ufightserver',
  });
}

//暴露出去给父组件使用
export interface CodeBranchExp {
  codeBranch: Ref<string>;
}

defineExpose<CodeBranchExp>({
  codeBranch: codeBranch,
});
</script>
