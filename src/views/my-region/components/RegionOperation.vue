<template>
  <div style="background-color: white; padding: 20px">
    <a-row :gutter="24">
      <a-col :span="12">
        <a-card title="区服操作" :bordered="true">
          <a-space wrap="wrap" gap="small" horizontal>
            <a-button
              key="startSf"
              :disabled="isLocked() || isActionDisabled('startSf')"
              @click="startSf"
            >
              启服
            </a-button>
            <a-popconfirm
              :title="`你确定停止该私服 ( ${currentSF} ) 吗?`"
              ok-text="确认"
              cancel-text="取消"
              :disabled="isLocked() || isActionDisabled('startSf')"
              @confirm="stopSf"
            >
              <a-button key="stopSf" danger :disabled="isLocked() || isActionDisabled('stopSf')">
                停服
              </a-button>
            </a-popconfirm>
            <a-popconfirm
              v-if="currentSFStatus.Locked"
              :title="`确认解锁 ( ${currentSF} ) 吗? 解锁后，你将恢复对私服的所有操作 `"
              ok-text="确认"
              cancel-text="取消"
              :disabled="isActionDisabled('unlockSf')"
              @confirm="updateSfStatus(false)"
            >
              <a-button key="unlockSf" danger :disabled="isActionDisabled('unlockSf')">
                解锁
              </a-button>
            </a-popconfirm>
            <a-popconfirm
              v-else
              ok-text="确认"
              cancel-text="取消"
              :disabled="isActionDisabled('lockSf')"
              @confirm="updateSfStatus(true)"
            >
              <template #title>
                <a-textarea
                  v-model:value="lockReason"
                  placeholder="请认真填写锁定原因!"
                ></a-textarea>
                <a-divider />
                确认锁定 {{ currentSF }} 吗? 锁定后将无法进行私服的任何操作!
              </template>
              <a-button key="lockSf" danger :disabled="isActionDisabled('lockSf')">锁定</a-button>
            </a-popconfirm>
            <a-form-item>
              <a-tooltip
                color="green"
                title="通常在发生私服更新失败的情况下，你都可以执行该操作，进行快速恢复，回退到上一个版本"
              >
                <a-popconfirm
                  :title="`你确定回滚该私服 ( ${currentSF} )  到上一个版本吗?`"
                  ok-text="确认"
                  cancel-text="取消"
                  :disabled="isLocked() || isActionDisabled('rollbackSf')"
                  @confirm="rollbackSf"
                >
                  <a-button key="1" danger :disabled="isLocked() || isActionDisabled('rollbackSf')">
                    回滚
                  </a-button>
                </a-popconfirm>
              </a-tooltip>
            </a-form-item>
          </a-space>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="模块操作" :bordered="true">
          <a-form layout="inline">
            <a-form-item label="模块">
              <a-select
                v-model:value="currentModule"
                style="width: 120px"
                :options="moduleData"
                default-active-first-option
              ></a-select>
            </a-form-item>
            <a-form-item style="">
              <a-dropdown>
                <template #overlay>
                  <a-menu :disabled="currentSFStatus.Locked">
                    <a-menu-item
                      key="killModule"
                      :disabled="isActionDisabled('killModule')"
                      @click="killModule"
                    >
                      强制重启
                    </a-menu-item>
                    <a-menu-item
                      key="stopModule"
                      title="模块被终止后，必须启动才可以进行强制重启操作"
                      :disabled="isActionDisabled('stopModule')"
                      @click="stopModule"
                    >
                      强制终止
                    </a-menu-item>
                    <a-menu-item
                      key="stopModule"
                      :disabled="isActionDisabled('startModule')"
                      @click="startModule"
                    >
                      启动模块
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button>
                  操 作
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script setup lang="ts">
import { isLocked, isActionDisabled } from '@/util/permission/action';
import { onMounted, ref } from 'vue';
import { useCurrentSFState } from '@/stores/modules/sf';
import { useUserStore } from '@/stores/modules/user';
import type { RegionStatus } from '@/api/model';
import {
  getAllModuleApi,
  killModuleApi,
  rollbackSfApi,
  startSfApi,
  stopSfApi,
  updateSfStatusApi,
} from '@/api/sfmanage';
import { DownOutlined } from '@ant-design/icons-vue';

let userState = useUserStore();
let sfState = useCurrentSFState(); // 获取sf pinia state

// 定义双向绑定的变量
const currentSF = ref<any>(sfState.currentValue);
const currentSFStatus = ref<RegionStatus>(sfState.getCurrentSFStatus);
const currentModule = ref<string>('gameserver');
const moduleData = ref<any[]>([]);
const lockReason = ref<string>('');

onMounted(() => {
  // console.log('操作模块加载........................................');
  getAllModule();
});

const emit = defineEmits(['refresh']);

/**
 * 回滚私服
 */
function rollbackSf() {
  rollbackSfApi({
    account: userState.getUSerName,
    region: currentSF.value,
    branch: '_', // 无需选定分支，直接回滚到上一个serverbin
  });
}

//启动私服
function startSf() {
  startSfApi({ account: userState.getUSerName, region: currentSF.value });
}

//
function stopSf() {
  stopSfApi({ account: userState.getUSerName, region: currentSF.value });
}

function killModule() {
  const res = killModuleApi({
    account: userState.getUSerName,
    region: currentSF.value,
    module_name: currentModule.value,
    // command: 'kill',
  });
}

function stopModule() {
  const res = killModuleApi({
    account: userState.getUSerName,
    region: currentSF.value,
    module_name: currentModule.value,
    command: 'stop',
  });
}

function startModule() {
  const res = killModuleApi({
    account: userState.getUSerName,
    region: currentSF.value,
    module_name: currentModule.value,
    command: 'start',
  });
}

function getAllModule() {
  const res = getAllModuleApi({ region: currentSF.value });
  res.then(data => {
    // // console.log('getAllModule', data);
    data.forEach((r: any) => {
      moduleData.value.push({ value: r, label: r });
    });
  });
}

function updateSfStatus(locked: boolean) {
  updateSfStatusApi({
    region: currentSF.value,
    locked: locked,
    describe: lockReason.value,
  }).then(data => {
    // console.log(data);
    sfState.setCurrentSFStatus(currentSF.value);
    sfState.setCurrentSfLock(locked);
    console.log('sfState.getCurrentSFStatus', sfState.getCurrentSFStatus, sfState.currentSFStatus);
    currentSFStatus.value = sfState.getCurrentSFStatus;
    // 可以传递 payload，父组件通过 $event 获取
    // emit('refresh');
    window.location.reload();
  });
}
</script>
