<template>
  <template v-if="!spinning && sfData?.length == 0">
    <page-container :title="route.meta.title" style="width: 100%">
      <div
        :style="{
          height: '100%',
          background: '#fff',
          padding: '20px',
        }"
      >
        <a-result title="你的账号下暂无任何私服的权限">
          <template #icon>
            <exclamation-circle-two-tone />
          </template>
          <template #extra>
            <p>请点击下方按钮跳转到申请页面</p>
            <a-button key="console" type="primary" size="large" @click="applyAction">
              <template #icon>
                <AppstoreAddOutlined />
              </template>
              去申请
            </a-button>
          </template>
        </a-result>
      </div>
    </page-container>
  </template>

  <a-layout v-else style="height: calc(100vh - 88px)">
    <!-- 使用 100vh 占满整个可视高度 -->
    <a-layout-sider
      :collapsed="state.collapsed"
      style="background: #fff"
      collapsible
      @collapse="toggleCollapsed"
    >
      <div class="ant-layout-sider-children">
        <a-select
          v-model:value="currentSF"
          show-search
          placeholder="请选择一个区服"
          style="width: 100%"
          :filter-option="filterOption"
          size="large"
          class="ant-select-selector"
          @focus="handleFocus"
          @blur="handleBlur"
          @change="handleRegionChange"
        >
          <a-select-option
            v-for="option in sfData"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          >
            <!--            <span role="img" aria-label="China">🇨🇳</span>-->
            <SwapOutlined />
            &nbsp; &nbsp;{{ option.value }}
            <span style="float: right" @click.stop="toggleFavorite(option)">
              <star-outlined v-if="!option.isFavorite" />
              <star-filled v-else />
            </span>
          </a-select-option>
        </a-select>
        <a-menu
          v-model:openKeys="state.openKeys"
          v-model:selectedKeys="state.selectedKeys"
          class="ant-menu ant-menu-root ant-menu-inline ant-menu-light ant-pro-sider-menu css-dev-only-do-not-override-19iuou"
          mode="inline"
          :inline-collapsed="state.collapsed"
          :items="items"
          @click="handleOnClick"
        ></a-menu>
      </div>
      <template #trigger>
        <div class="custom-trigger">
          <MenuUnfoldOutlined v-if="state.collapsed" />
          <MenuFoldOutlined v-else />
        </div>
      </template>
    </a-layout-sider>
    <a-layout-content>
      <page-container :title="route.meta.title">
        <template #content>
          <a-descriptions title="基本信息">
            <a-descriptions-item label="区服名字">{{ currentSF }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ currentSFStatus.created_time }}
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ currentSFStatus.updated_time }}
            </a-descriptions-item>
            <a-descriptions-item>
              <p>
                <a-space>
                  当前区服时间：{{ currentSfTime }}
                  <a-tag v-if="timeDelta < 0" color="red">
                    比实际时间
                    <b>早</b>
                    {{ dateMsg }}
                  </a-tag>
                  <a-tag v-else-if="timeDelta > 0" color="red">
                    比实际时间
                    <b>晚</b>
                    {{ dateMsg }}
                  </a-tag>
                  <a-tag v-else color="green">
                    {{ dateMsg }}
                  </a-tag>
                </a-space>
              </p>
            </a-descriptions-item>
            <a-descriptions-item label="服务时间最近操作">
              【{{ currentSFStatus.latest_change_time_user }}】 修改于 【{{
                currentSFStatus.latest_change_time_at
              }}】
            </a-descriptions-item>
            <a-descriptions-item label="最近操作">
              【{{ currentSFStatus.latest_operate_user }}】 在 【{{
                currentSFStatus.latest_operate_time
              }}】 进行 【{{ currentSFStatus.latest_operate_action }}】
              <a href="/my-region/region-operate">查看详情</a>
            </a-descriptions-item>
          </a-descriptions>
        </template>
        <a-alert
          v-if="currentSFStatus.Locked"
          :message="
            '警告：私服已经被锁定，无法进行任何操作. 锁住人：' +
            currentSFStatus.locker +
            ' 锁住原因：' +
            currentSFStatus.describe
          "
          type="error"
          closable
          style="margin-bottom: 10px; text-align: center"
        />
        <template #extra>
          <a-form layout="inline">
            <a-form-item label="健康状态">
              <a-tag v-if="currentSFStatus.healthy_status === 1" color="green">
                <template #icon>
                  <sync-outlined :spin="true" />
                </template>
                运行中
              </a-tag>
              <a-tag v-else-if="currentSFStatus.healthy_status === 2" color="orange">
                <template #icon>
                  <exclamation-circle-outlined />
                </template>
                <a-tooltip title="私服刚刚成功更新后，需要等待约2分钟，状态会变为运行中">
                  启动时间小于80s
                </a-tooltip>
              </a-tag>
              <a-tag v-else-if="currentSFStatus.healthy_status === 3" color="red">
                <template #icon>
                  <close-circle-outlined />
                </template>
                异常
              </a-tag>
              <a-tag v-else color="red">
                <template #icon>
                  <minus-circle-outlined />
                </template>
                未知
              </a-tag>
            </a-form-item>
            <a-form-item label="任务状态">
              <a-tag v-if="currentSFStatus.status == 1" color="green">
                <template #icon>
                  <check-circle-outlined />
                </template>
                已完成
              </a-tag>
              <a-tag v-else-if="currentSFStatus.status == 2" color="orange">
                <template #icon>
                  <loading-outlined />
                </template>
                更新中
              </a-tag>
              <a-tag v-else-if="currentSFStatus.status == 3" color="orange">
                <template #icon>
                  <minus-circle-outlined />
                </template>
                错误
              </a-tag>
              <a-tag v-else-if="currentSFStatus.status == 4" color="#2db7f5">
                <template #icon>
                  <minus-circle-outlined />
                </template>
                未知状态
              </a-tag>
              <a-tag v-else-if="currentSFStatus.status == 5" color="purple">
                <template #icon>
                  <minus-circle-outlined />
                </template>
                运行超时
              </a-tag>
              <a-tag v-else color="red">
                <template #icon>
                  <minus-circle-outlined />
                </template>
                已停止
              </a-tag>
            </a-form-item>
            <a-form-item>
              <a-popover
                v-model:open="operationVisible"
                :title="currentSF"
                trigger="click"
                placement="bottom"
              >
                <template #content>
                  <region-operation @refresh="refresh"></region-operation>
                </template>
                <a-button type="">操作</a-button>
              </a-popover>
            </a-form-item>
            <a-form-item>
              <a-button key="refresh" shape="circle" @click="refresh">
                <RedoOutlined />
              </a-button>
            </a-form-item>
          </a-form>
        </template>
        <template #extraContent>
          <a-space>
            <a-statistic
              title="玩家在线人数"
              :value="currentSFStatus.user_count === -1 ? '未知' : currentSFStatus.user_count"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-statistic>
            <a-statistic title="今日更新" :value="todayUpdateCount">
              <template #prefix>
                <CalendarOutlined />
              </template>
            </a-statistic>
            <a-statistic title="总计更新" :value="totalUpdateCount">
              <template #prefix>
                <LikeOutlined />
              </template>
            </a-statistic>
          </a-space>
        </template>
        <div class="page-header-index-wide">
          <a-card
            :bordered="false"
            :body-style="{ padding: '16px 0', height: '100%' }"
            :style="{ height: '' }"
          >
            <div class="router-info-right">
              <transition name="fade" mode="out-in">
                <router-view :key="viewKey" />
              </transition>
            </div>
          </a-card>
        </div>
      </page-container>
    </a-layout-content>
  </a-layout>
</template>
<script lang="ts" setup>
import { reactive, watch, onBeforeMount, onUnmounted } from 'vue';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  ExclamationCircleTwoTone,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RedoOutlined,
  ExclamationCircleOutlined,
  LikeOutlined,
  LoadingOutlined,
  MinusCircleOutlined,
  SwapOutlined,
  SyncOutlined,
  UserOutlined,
} from '@ant-design/icons-vue';
import { message, type SelectProps } from 'ant-design-vue';
import { ref, computed } from 'vue';
import { getUserRegionApi, getUserRegionPermApi } from '@/api/permission';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '@/stores/modules/user';
import { useCurrentSFState } from '@/stores/modules/sf';
import type { RegionStatus } from '@/api/model';
import { getServerTimeOffsetApi, getSfStatusApi, getUpdateStatsApi } from '@/api/sfmanage';
import RegionOperation from '@/views/my-region/components/RegionOperation.vue';
import { AppstoreAddOutlined, StarOutlined, StarFilled } from '@ant-design/icons-vue';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import duration from 'dayjs/plugin/duration';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);

const route = useRoute();
const router = useRouter();
const items = ref([]);

let userState = useUserStore();
let sfState = useCurrentSFState(); // 获取sf pinia state
const viewKey = ref(0);

// 定义双向绑定的变量
const currentSF = ref<any>(sfState.currentValue);
// const currentSFStatus = ref<RegionStatus>({});
const currentSfTime = ref<string>('');

const currentSFStatus = computed(() => sfState.currentSFStatus);

const currentSFActions = ref<string[]>(sfState.getCurrentSFActions);

const selectedKeys = ref<string[]>([]);
const openKeys = ref<string[]>([]);
const spinning = ref<boolean>(true);

const todayUpdateCount = ref<number>(0);
const totalUpdateCount = ref<number>(0);
const operationVisible = ref<boolean>(false);

const state = reactive({
  collapsed: false,
  selectedKeys: [route.path],
  openKeys: ['sub1'],
  preOpenKeys: ['sub1'],
});
watch(
  () => route.path,
  (_val, oldVal) => {
    state.preOpenKeys = oldVal;
  },
);
const toggleCollapsed = () => {
  state.collapsed = !state.collapsed;
  state.openKeys = state.collapsed ? [] : state.preOpenKeys;
};

const sfData = ref<SelectProps['options']>([]);
const getParentRoute = (path: string) => {
  const segments = path.split('/').filter(Boolean); // Split by '/' and remove empty elements
  return segments.length > 0 ? `/${segments[0]}` : '/';
};
onBeforeMount(() => {
  getAllSfName(); //获取所有私服名字
  // 获取所有路由
  const allRoutes = router.getRoutes();
  // 找到当前路由
  const currentRoute = allRoutes.find(r => r.path === getParentRoute(route.path));
  // 返回当前路由的子路由
  // console.log(currentRoute ? currentRoute.children : []);
  const currentChildren = currentRoute ? currentRoute.children : [];
  currentChildren.forEach(item => {
    items.value.push({
      key: getParentRoute(route.path) + '/' + item.path,
      icon: item.meta.icon,
      label: item.meta.title || '',
      title: item.meta.name || '',
    });
  });
});

function getAllSfName() {
  getUserRegionApi().then(data => {
    data.items.forEach((r: any) => {
      sfData.value.push({ value: r, label: r, isFavorite: false });
    });
    mergeAndSortOptions(sfData.value);

    spinning.value = false;
    const defaultSf = localStorage.getItem('currentSF') || (data.items ? data.items[0] : '');
    currentSF.value = defaultSf;
    if (defaultSf !== undefined) {
      sfState.setCurrentSF(defaultSf); //首次加载时，设置state的值
    }
    if (currentSF.value) {
      getSfStatus();
      getSfActions();
    }
    if (statusTimer == null && currentSF.value) {
      statusTimer = setInterval(getSfStatus, 5000); //获取选中私服的状态 1：表示启动 0： 表是停止
    }
  });
}

const handleBlur = () => {
  // console.log('blur');
};
const handleFocus = () => {
  // console.log('focus');
};
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const handleOnClick = eventData => {
  // console.log('onClick', eventData);
  const routePath = eventData.key; // 获取选中的菜单项的 key
  router.push(routePath); // 使用 router.push 进行路由跳转
};

watch(route, (newValue, oldValue) => {
  // console.log('值发生了变更', newValue, oldValue, route.name);
  if (currentSF.value) {
    // 只有在有选中区服时才重新获取状态
    getSfStatus(); // 这会重新获取状态并启动定时器
  }
  updateMenu();
});

//为了打开页面后，选中菜单，只为active样式
function updateMenu() {
  const newName = route.name?.toString();
  // console.log('newName: ', route.name);
  selectedKeys.value = [newName!];
}

//组件的生命周期方法，组件被渲染完成时执行
onBeforeMount(() => {
  console.log(`the component is now mounted.（clearTimer）`);
  clearTimer();
  updateMenu();
});

let statusTimer: number | undefined = undefined;

function getSfActions() {
  getUserRegionPermApi(currentSF.value).then(data => {
    // console.log('getUserRegionPermApi: ', data);
    sfState.setCurrentSFActions(data);
  });
}

function getUpdateStats() {
  if (currentSF.value === undefined) {
    return;
  }
  getUpdateStatsApi(currentSF.value).then(data => {
    todayUpdateCount.value = data.today_count;
    totalUpdateCount.value = data.total_count;
  });
}

onUnmounted(() => {
  // console.log('onUnmounted statusTimer:', statusTimer);
  clearTimer();
});

function clearTimer() {
  if (statusTimer) {
    console.log('clear statusTimer:', statusTimer);
    clearInterval(statusTimer);
    statusTimer = undefined;
  }
  if (timeFun) {
    console.log('clear timeFun:', timeFun);
    clearInterval(timeFun);
    timeFun = undefined;
  }
}

function getSfStatus() {
  if (currentSF.value === undefined || currentSF.value === 'undefined') {
    return;
  }
  // clearTimer(); // 获取新状态前清除定时器
  getUpdateStats();
  sfState.setCurrentSFStatus(currentSF.value);
  // 获取区服状态的同时，获取私服时间
  showModifyServerTime();
}

function handleRegionChange() {
  sfState.setCurrentSF(currentSF.value); //当下拉框发生变化时，记得改变state的值
  getSfStatus(); //重新获取私服状态
  getSfActions();
  viewKey.value++; // Increment the key to trigger re-render
}

function refresh() {
  sfState.refresh();
  message.success('刷新成功');
  handleRegionChange();
  viewKey.value++; // Increment the key to trigger re-render
}

function applyAction() {
  router.push({ name: 'perm-apply' });
}

// =================区服收藏 ===============================
const sortOptions = () => {
  const favorites = sfData.value.filter(option => option.isFavorite);
  const nonFavorites = sfData.value.filter(option => !option.isFavorite);
  sfData.value = [...favorites, ...nonFavorites];
};
// 用于排序
const toggleFavorite = (option: SelectProps['options'][0]) => {
  option.isFavorite = !option.isFavorite;
  sortOptions();
  saveFavoritesToLocalStorage();
};
// 保存收藏的区服到localstorage
const saveFavoritesToLocalStorage = () => {
  const favorites = sfData.value.map(option => ({
    value: option.value,
    isFavorite: option.isFavorite,
  }));
  localStorage.setItem('favorites', JSON.stringify(favorites));
};
// 加载收藏的区服
const loadFavoritesFromLocalStorage = () => {
  const favorites = localStorage.getItem('favorites');
  if (favorites) {
    return JSON.parse(favorites);
  }
  return [];
};
// 合并本地localstorage中进行收藏 排序后的区服
const mergeAndSortOptions = (backendOptions: SelectProps['options']) => {
  const favorites = loadFavoritesFromLocalStorage();
  const optionsMap = new Map<string, SelectProps['options'][0]>();

  // 将后端数据放入 Map
  backendOptions.forEach(option => {
    optionsMap.set(option.value, option);
  });

  // 合并后端数据和本地收藏状态
  const mergedOptions = favorites
    .map(favorite => {
      const option = optionsMap.get(favorite.value);
      if (option) {
        return { ...option, isFavorite: favorite.isFavorite };
      }
      return null;
    })
    .filter((option): option is SelectProps['options'][0] => option !== null);

  // 添加后端数据中不存在于本地收藏状态的选项
  backendOptions.forEach(option => {
    if (!favorites.some(favorite => favorite.value === option.value)) {
      mergedOptions.push({ ...option, isFavorite: false });
    }
  });

  // 根据收藏状态和顺序进行排序
  const favoritesList = mergedOptions.filter(option => option.isFavorite);
  const nonFavoritesList = mergedOptions.filter(option => !option.isFavorite);
  sfData.value = [...favoritesList, ...nonFavoritesList];
};
// ================= 区服收藏 ===============================

// ================= 私服服务器时间 ===============================
let timeFun = null;
const timeDelta = ref();
const dateMsg = ref();

function showModifyServerTime() {
  getServerTimeOffsetApi({ region: currentSF.value }).then(data => {
    timeDelta.value = data.offset;
    if (data.offset != 0) {
      dateMsg.value = getDeltaTime(data.offset);
    } else {
      dateMsg.value = '与实际时间一致';
    }
    setCurrentSfTime(data.current_time, data.offset);

    //初始化定时器
    if (timeFun) {
      // 如果已经有定时器，先清除
      clearInterval(timeFun);
    }
    timeFun = setInterval(increaseCurrentSfTime, 1000);
  });
}

function setCurrentSfTime(current_time: number, second: number) {
  currentSfTime.value = dayjs
    .unix(current_time)
    .tz('Asia/Shanghai')
    .add(second, 'second')
    .format('YYYY/MM/DD HH:mm:ss Z');
  if (sfState.currentSF.endsWith('_us')) {
    // 美服采用西五区
    currentSfTime.value = dayjs
      .unix(current_time)
      .tz('Etc/GMT+5')
      .add(second, 'second')
      .format('YYYY/MM/DD HH:mm:ss Z');
  } else if (sfState.currentSF.endsWith('_eu')) {
    // 欧服采用东一区
    currentSfTime.value = dayjs
      .unix(current_time)
      .tz('Etc/GMT-1')
      .add(second, 'second')
      .format('YYYY/MM/DD HH:mm:ss Z');
  }
}

function increaseCurrentSfTime() {
  setCurrentSfTime(dayjs(currentSfTime.value).unix(), 1);
}

function getDeltaTime(offset: number) {
  const deltaAbs = Math.abs(offset);

  const deviation = dayjs.duration(deltaAbs * 1000);
  let day = deviation.days();
  let hour = deviation.hours();
  let minute = deviation.minutes();
  let second = deviation.seconds();
  let month = deviation.months();
  let year = deviation.years();
  let days = deviation.asDays();
  // 如果超过30天，将month和year转化为day
  if (month > 0 || year > 0) {
    day = Math.floor(days);
  }

  let d = day < 10 ? '0' + day : day;
  let h = hour < 10 ? '0' + hour : hour;
  let m = minute < 10 ? '0' + minute : minute;
  let s = second < 10 ? '0' + second : second;

  if (deltaAbs < 60) {
    return `${s}秒`;
  } else if (deltaAbs < 60 * 60) {
    return `${m}分钟${s}秒`;
  } else if (deltaAbs < 60 * 60 * 24) {
    return `${h}小时${m}分钟${s}秒`;
  } else {
    return `${d}天${h}小时${m}分钟${s}秒`;
  }
}

// ================= 私服服务器时间 ===============================
</script>

<style lang="less" scoped>
.router-info-right {
  padding: 10px 10px;
}

.ant-select-selector {
  display: flex;
  align-items: center;
  justify-content: center; /* Center text horizontally */
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}

.custom-trigger {
  background-color: white;
  border-top: 1px solid #f0f0f0;
  color: black;
}
</style>
