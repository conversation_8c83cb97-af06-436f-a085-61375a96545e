<template>
  <div>
    <a-row>
      <a-space>
        <a-tooltip
          color="green"
          title="通常在任何情况下，你都可以执行该操作，进行私服强制更新与启动"
        >
          <a-button
            key="updateSfBinAndDataDanger"
            type="primary"
            :danger="codeBranch === 'release_week'"
            :disabled="isLocked() || isActionDisabled('updateSfBinAndData')"
            :loading="updateSfAllLoading"
            @click="updateSfBinAndData"
          >
            <div v-if="dataBranch === 'localDesignData'">
              一键更新私服代码到最新 {{ codeBranch }} 并加载数值
            </div>
            <div v-else>
              一键更新私服到最新 {{ codeBranch }} / {{ dataBranch }}
            </div>
          </a-button>
        </a-tooltip>
        <ServerTime></ServerTime>
        <PatchModal
          :region="currentSF"
          :disabled="isLocked() || isActionDisabled('deployPatch')"
        ></PatchModal>
        <a-tooltip color="green" title="更新xml配置并重载，注意部分配置需要重启生效">
          <a-button
            key="deployServerXml"
            type="default"
            :disabled="isLocked() || isActionDisabled('editXml')"
            :loading="updateServerXmlLoading"
            @click="deployServerXml"
          >
            更新xml配置
          </a-button>
        </a-tooltip>
        <DeleteUserData></DeleteUserData>
        <SimpleRepair></SimpleRepair>
      </a-space>
    </a-row>
    <a-divider />
    <a-row>
      <CodeBranch ref="codeBranchRef"></CodeBranch>
      <DataBranch ref="dataBranchRef"></DataBranch>
    </a-row>
    <a-divider></a-divider>
    <!--    <a-row>-->
    <!--      <UfightCodeBranch ref="ufightCodeBranchRef"></UfightCodeBranch>-->
    <!--      <UfightDataBranch ref="ufightDataBranchRef"></UfightDataBranch>-->
    <!--    </a-row>-->
  </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeUnmount, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useCurrentSFState } from '@/stores/modules/sf';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  CodeBranch,
  DataBranch,
  ServerTime,
  UfightCodeBranch,
  UfightDataBranch,
  SimpleRepair,
} from './components/index';
import { useUserStore } from '@/stores/modules/user';
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import type { CodeBranchExp } from '@/views/my-region/components/CodeBranch.vue';
import type { DataBranchExp } from '@/views/my-region/components/DataBranch.vue';
import { updateSfBinAndDataApi, updateServerXmlApi } from '@/api/sfmanage';
import { isLocked, isActionDisabled } from '@/util/permission/action';
import PatchModal from '@/views/my-region/components/PatchModal.vue';
import DeleteUserData from '@/views/my-region/components/DeleteUserData.vue';
import { checkMirrorConfig, showConfirm } from '@/util/common';

//加载时间插件
dayjs.extend(duration); //Duration 增加了 .duration .isDuring API 来支持时间长度。

const route = useRoute();
const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服
const currentSFStatus = computed(() => sfState.getCurrentSFStatus); // 建立响应式依赖

onMounted(() => {
  // console.log('  sfStatus.value = currentSFStatus.value;', currentSFStatus.value);
  // sfStatus.value = currentSFStatus.value;
});
onBeforeUnmount(() => {
  // 订阅私服 下拉框选择 发生变化，要记得改变当前的 私服名字
  // console.log('the component is before mount.');
});

// 父组件 重新路由后 通过prop向子组件传参
// const props = defineProps({ current-sf-prop: String, });

const codeBranchRef = ref<CodeBranchExp>(null); // 通过 ref 绑定子组件
const dataBranchRef = ref<DataBranchExp>(null); // 通过 ref 绑定子组件

const codeBranch = computed(() => codeBranchRef.value?.codeBranch);
const dataBranch = computed(() => dataBranchRef.value?.dataBranch);

const updateSfAllLoading = ref<boolean>(false);
const updateServerXmlLoading = ref<boolean>(false);

// 更新代码与数值
async function updateSfBinAndData() {
  updateSfAllLoading.value = true;
  console.log(currentSFStatus.value.mirror_config);
  // 检查 mirror_config 是否存在
  let actionName = `【一键更新私服到最新 ${codeBranch.value} / ${dataBranch.value}】`;
  const confirmed = await checkMirrorConfig(
    currentSFStatus.value,
    'region:updateSfBinAndData',
    actionName,
  );
  if (!confirmed) {
    updateSfAllLoading.value = false;
    return;
  }

  const dataRevisionObj = dataBranchRef.value.dataBranchInfo.find(d => d.key === "revision")
  const dataRevision = dataRevisionObj ? dataRevisionObj.value : ""
  // 只有确认后或者不需要确认时，才执行后续操作
  console.log('--------------------start update all--------------------------');
  // 这里继续执行更新逻辑...
  await updateSfBinAndDataApi({
    account: userState.getUSerName,
    region: currentSF,
    branch: codeBranch.value,
    data_branch: dataBranch.value,
    upload_operation_zip: dataBranchRef.value.uploadOperationZip,
    data_revision: dataRevision,
  }).catch(error => {
    console.log(error);
  });
  updateSfAllLoading.value = false;
}

async function deployServerXml() {
  // console.log('deployServerXmlData', codeBranch.value);
  updateServerXmlLoading.value = true;
  const res = await updateServerXmlApi({
    region: currentSF,
    branch: codeBranch.value,
  }).catch(error => {
    // console.log(error);
  });
  updateServerXmlLoading.value = false;
}
</script>
