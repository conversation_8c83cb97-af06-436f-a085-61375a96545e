<template>
  <div>
    <a-alert type="info" closable>
      <template #message>温馨提示：此处操作只针对 Ufightserver 模块进行代码或者数值更新</template>
    </a-alert>
    <a-divider></a-divider>
    <a-row>
      <UfightCodeBranch ref="ufightCodeBranchRef"></UfightCodeBranch>
      <UfightDataBranch ref="ufightDataBranchRef"></UfightDataBranch>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onBeforeUnmount, createVNode, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useCurrentSFState } from '@/stores/modules/sf';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  CodeBranch,
  DataBranch,
  ServerTime,
  UfightCodeBranch,
  UfightDataBranch,
} from './components/index';
import { Modal } from 'ant-design-vue';
import { useUserStore } from '@/stores/modules/user';
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import type { CodeBranchExp } from '@/views/my-region/components/CodeBranch.vue';
import type { DataBranchExp } from '@/views/my-region/components/DataBranch.vue';
import { deleteSfUserApi, updateSfBinAndDataApi } from '@/api/sfmanage';
//加载时间插件
dayjs.extend(duration); //Duration 增加了 .duration .isDuring API 来支持时间长度。

const route = useRoute();
const sfState = useCurrentSFState(); // 获取sf pinia state
const userState = useUserStore();
let currentSF = sfState.currentValue; // 获取当前下拉框 选择的私服

onBeforeUnmount(() => {
  // 订阅私服 下拉框选择 发生变化，要记得改变当前的 私服名字
  // console.log('the component is before mount.');
});

// 父组件 重新路由后 通过prop向子组件传参
// const props = defineProps({ current-sf-prop: String, });

const codeBranchRef = ref<CodeBranchExp>(null); // 通过 ref 绑定子组件
const dataBranchRef = ref<DataBranchExp>(null); // 通过 ref 绑定子组件

const codeBranch = computed(() => codeBranchRef.value?.codeBranch);
const dataBranch = computed(() => dataBranchRef.value?.dataBranch);

// 更新代码与数值
function updateSfBinAndData() {
  // console.log('updateSfBinAndData', codeBranch.value, dataBranch.value);
  updateSfBinAndDataApi({
    account: userState.getUSerName,
    region: currentSF,
    branch: codeBranch.value,
    data_branch: dataBranch.value,
  });
}

// 清空私服数据
function showConfirm() {
  Modal.confirm({
    title: '重要提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: `此操作将永久删除该私服【${currentSF}】下【所有】用户数据, 是否继续?`,
    onOk() {
      deleteSfUser();
    },
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel() {
      // console.log('onCancel');
    },
  });
}

function deleteSfUser() {
  // console.log('deleteSfUser');
  deleteSfUserApi({ region: currentSF, account: userState.getUSerName });
}
</script>
