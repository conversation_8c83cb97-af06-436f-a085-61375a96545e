<template>
  <div>
    <a-alert v-if="description" style="margin-bottom: 10px" type="warning" closable>
      <template #description>
        {{ description }}
        <a href="https://app.hoyowave.com/service-hub?id=77810113271606" target="_blank">
          <a-button type="link" danger>点击立即咨询！</a-button>
        </a>
      </template>
    </a-alert>
    <template v-if="logsType === 'gameserver_log'">
      <a-form layout="inline" style="margin-bottom: 10px">
        <a-form-item label="搜索">
          <a-input v-model:value="logSearch" placeholder="请输入搜索内容">
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="日志类型">
          <a-select
            ref="select"
            v-model:value="logType"
            style="width: 187px"
            :options="logTypeOptions"
          ></a-select>
        </a-form-item>
      </a-form>

      <div>
        <a-spin tip="正在加载日志中,请稍后..." :spinning="spinning">
          <div class="background">
            <MonacoEditor v-model="filteredServerLogs" read-only width="100%" height="700px" />
          </div>
        </a-spin>
      </div>
    </template>
    <template v-else>
      <div>
        <a-spin tip="正在加载日志中,请稍后..." :spinning="spinning">
          <div class="background">
            <MonacoEditor v-model="taskLogs" read-only width="100%" height="700px" />
          </div>
        </a-spin>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import type { SelectProps } from 'ant-design-vue';
import { useRoute } from 'vue-router';
import { SearchOutlined } from '@ant-design/icons-vue';
import { useCurrentSFState } from '@/stores/modules/sf';
import { getTaskLogApi } from '@/api/sfmanage';
import MonacoEditor from '@/components/monaco/monaco.vue';

const sfState = useCurrentSFState(); // 获取sf pinia state
const route = useRoute(); // 获取当前路由
const router = useRouter();

const logs = ref<string[]>([]);
const spinning = ref<boolean>(false);

const description = ref<string>('');

const logType = ref<string>('Error');
const logsType = ref<string>('task_log');
const logSearch = ref<string>('');
const logTypeOptions = ref<SelectProps['options']>([
  {
    value: 'All',
    label: 'All 所有日志',
  },
  {
    value: 'Debug',
    label: 'Debug 详细日志',
  },
  {
    value: 'Info',
    label: 'Info 信息日志',
  },
  {
    value: 'Warning',
    label: 'Warning 警告日志',
  },
  {
    value: 'Error',
    label: 'Error 错误日志',
  },
]);
const filteredServerLogs = computed(() => {
  return logs.value
    .filter(item => {
      const containSearch = logSearch.value === '' || item.includes(logSearch.value);
      const containsLevel =
        logType.value === 'All' || item.includes(`LOG_${logType.value.toUpperCase()}`);
      return containSearch && containsLevel;
    })
    .join('\n');
});
const taskLogs = computed(() => {
  return logs.value.join('\n');
});
onMounted(async () => {
  const task_id = route.query.task_id as string;
  const region = route.query.region as string;
  if (region) {
    sfState.setCurrentSF(region);
  }

  await getTaskLog(task_id);
  if (region || task_id) {
    // 删掉query中参数
    router.replace({
      query: {},
    });
  }
});

function getTaskLog(task_id: string) {
  if (!sfState.currentValue) {
    return;
  }
  spinning.value = true;
  let query = { region: sfState.currentValue, task_id };
  getTaskLogApi(query).then(data => {
    spinning.value = false;
    if (data != null && data.logs.length > 0) {
      logsType.value = data.logs_type;
      logs.value = data.logs;
      if (logsType.value === 'gameserver_log') {
        description.value =
          '提示：一般情况下为数值发生错误，请尝试将私服服务器代码更到最新，再次尝试。如错误仍然存在，请检查数值配置是否需要进行修改或删除。';
      } else {
        description.value =
          '提示：如果你查看日志后，进行仔细排查，再次尝试一键更新，问题仍然继续， 请联系管理员进行咨询。';
      }
      return;
    }
    logs.value = ['没有获取到任务执行过程中的任何错误日志，请查看 游戏日志 或 联系管理员'];
  });
}
</script>
