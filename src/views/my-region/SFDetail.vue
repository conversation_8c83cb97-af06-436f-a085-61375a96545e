<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1" tab="概览">
      <template v-if="regionInfo.items.length > 0 && serverDetail.dataSource.length > 0">
        <a-descriptions :title="currentSF" bordered>
          <a-descriptions-item label="开服时间">
            {{ regionInfo.items[0]['stop_end_time'] }}
          </a-descriptions-item>
          <a-descriptions-item label="停服时间">
            {{ regionInfo.items[0]['stop_begin_time'] }}
          </a-descriptions-item>
          <a-descriptions-item label="Dispatch Url">
            {{ regionInfo.items[0]['dispatch_url'] }}
          </a-descriptions-item>
          <a-descriptions-item label="GateServer">
            {{
              serverDetail.dataSource.find(item => item.des === 'gate')['outer_ip'] +
              ':' +
              serverDetail.dataSource.find(item => item.des === 'gate')['listen_port']
            }}
          </a-descriptions-item>
          <a-descriptions-item
            v-for="[key, value] in Object.entries(regionDetail)"
            :key="key"
            :label="key"
          >
            {{ value }}
          </a-descriptions-item>
          <a-descriptions-item label="网盘链接地址">
            \\{{ serverDetail.dataSource.find(item => item.des === 'gate')['outer_ip'] }}\nap_smb_{{
              regionDetail['samba_user']
            }}
          </a-descriptions-item>
          <a-descriptions-item label="帮助文档">
            <a href="https://km.mihoyo.com/doc/mhn1omipc1ng">windows挂载私服samba网盘文档</a>
          </a-descriptions-item>
        </a-descriptions>
      </template>
    </a-tab-pane>
    <a-tab-pane key="2" tab="Region">
      <a-descriptions :title="currentSF" bordered>
        <a-descriptions-item v-for="column in regionInfo.columns" :key="column" :label="column">
          {{ regionInfo.items[0][column] }}
        </a-descriptions-item>
      </a-descriptions>
    </a-tab-pane>
    <a-tab-pane key="3" tab="Mysql" force-render>
      <a-table :data-source="mysqlDetail.dataSource" :columns="mysqlDetail.columns" />
    </a-tab-pane>
    <a-tab-pane key="4" tab="Redis">
      <a-table :data-source="redisDetail.dataSource" :columns="redisDetail.columns" />
    </a-tab-pane>
    <a-tab-pane key="5" tab="Server">
      <a-table :data-source="serverDetail.dataSource" :columns="serverDetail.columns" />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import {
  getMysqlDetailApi,
  getRedisDetailApi,
  getRegionInfoApi,
  getServerDetailApi,
} from '@/api/dbtool';
import { useUserStore } from '@/stores/modules/user';
import { useCurrentSFState } from '@/stores/modules/sf';
import type { RegionStatus } from '@/api/model';
import { getRegionDetailApi } from '@/api/sfmanage';

const activeKey = ref('1');
let userState = useUserStore();
let sfState = useCurrentSFState(); // 获取sf pinia state
const regionDetail = ref({});
// 定义双向绑定的变量
const currentSF = ref<any>(sfState.currentValue);
const currentSFStatus = ref<RegionStatus>(sfState.getCurrentSFStatus);
const regionInfo = ref({
  columns: [],
  items: [],
});
const mysqlDetail = ref({
  columns: [],
  dataSource: [],
});
const redisDetail = ref({
  columns: [],
  dataSource: [],
});
const serverDetail = ref({
  columns: [],
  dataSource: [],
});

onBeforeMount(async () => {
  await getRegionInfo();
  await getMysqlDetail();
  await getRedisDetail();
  await getServerDetail();
  await getRegionDetail();
});

async function getRegionInfo() {
  const resp = await getRegionInfoApi(currentSF.value);
  // console.log(resp);
  delete resp.items[0]['server_secret_key'];
  delete resp.items[0]['client_secret_key'];
  regionInfo.value = resp;
}

async function getMysqlDetail() {
  const resp = await getMysqlDetailApi(currentSF.value);
  mysqlDetail.value.dataSource = resp.items;
  resp.columns?.forEach(column => {
    mysqlDetail.value.columns.push({
      title: column,
      dataIndex: column,
      key: column,
    });
  });
}

async function getRedisDetail() {
  const resp = await getRedisDetailApi(currentSF.value);
  redisDetail.value.dataSource = resp.items;
  resp.columns?.forEach(column => {
    redisDetail.value.columns.push({
      title: column,
      dataIndex: column,
      key: column,
    });
  });
}

async function getServerDetail() {
  const resp = await getServerDetailApi(currentSF.value);
  serverDetail.value.dataSource = resp.items;
  resp.columns?.forEach(column => {
    serverDetail.value.columns.push({
      title: column,
      dataIndex: column,
      key: column,
    });
  });
}

const getRegionDetail = async () => {
  regionDetail.value = await getRegionDetailApi(currentSF.value);
};
</script>
