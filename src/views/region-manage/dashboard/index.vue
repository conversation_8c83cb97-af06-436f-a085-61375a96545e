<template>
  <page-container :title="`${route.meta.title} `">
    <template #content>
      <a-spin :spinning="spinning" style="float: right" size="large">
        <a-descriptions title="区服时间">
          <a-descriptions-item label="开服时间">
            {{ regionConfig.stop_end_time }}
          </a-descriptions-item>
          <a-descriptions-item label="停服时间">
            {{ regionConfig.stop_begin_time }}
          </a-descriptions-item>

          <a-descriptions-item label="Title">
            {{ regionConfig.title }}
          </a-descriptions-item>
          <a-descriptions-item label="数值就绪时间">
            {{ regionConfig.data_ready_time }}
          </a-descriptions-item>
          <a-descriptions-item label="Dispatch Seed">
            {{ regionConfig.dispatch_seed }}
          </a-descriptions-item>
          <a-descriptions-item label="stop_msg">
            {{ regionConfig.stop_msg }}
          </a-descriptions-item>
        </a-descriptions>
        <a-divider></a-divider>

        <a-descriptions title="跑马灯配置">
          <a-descriptions-item label="ID">
            {{ regionAnnounce.id }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ regionAnnounce.begin_time }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ regionAnnounce.end_time }}
          </a-descriptions-item>
          <a-descriptions-item label="platform_type_list">
            {{ regionAnnounce.platform_type_list }}
          </a-descriptions-item>
          <a-descriptions-item label="count_down_frequency">
            {{ regionAnnounce.count_down_frequency }}
          </a-descriptions-item>
          <a-descriptions-item label="modify_time">
            {{ regionAnnounce.modify_time }}
          </a-descriptions-item>
        </a-descriptions>
        <a-divider></a-divider>
        <a-descriptions title="登录奖励"></a-descriptions>
        <a-table :data-source="regionLoginReward" :columns="regionLoginRewardColumns" bordered/>
      </a-spin>
    </template>
  </page-container>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  getRegionLoginRewardApi,
  getRegionConfigDetailApi,
  getRegionAnnounceApi,
} from '@/api/dbtool';

const router = useRouter();
const route = useRoute();

const spinning = ref<boolean>(true);

const regionConfig = ref({});
const regionAnnounce = ref({});
const regionLoginReward = ref([]);
const regionLoginRewardColumns = ref([]);
// const pagination = ref({ total: 10, current: 1, pageSize: 5 });
// 当前选中的区域（全局变量）
const currentRegion = ref<string>('');
onMounted(() => {
  getRegionConfigDetailApi(currentRegion.value).then(data => {
    regionConfig.value = data.items[0];
  });

  getRegionAnnounceApi(currentRegion.value).then(data => {
    regionAnnounce.value = data.items[0];
  });

  getRegionLoginRewardApi(currentRegion.value).then(data => {
    console.log('--------------------------------->:', data);
    regionLoginReward.value = data.items;
    console.log('rlr.columns: ', data.columns);
    data.columns.forEach((item: string) => {
      regionLoginRewardColumns.value.push({
        title: item,
        dataIndex: item,
        key: item,
      });
    });
    spinning.value = false;
    console.log('regionAnnounceColumns.value: ', regionLoginRewardColumns.value);
  });
});
// 监听路由变化，更新选中状态
watch(
  () => route.path,
  newPath => {
    // 从路径中提取区域和功能
    const [, , region, ...funcs] = newPath.split('/');
    if (region) {
      currentRegion.value = region; // 更新全局区域变量
    }
  },
  { immediate: true },
);
</script>
