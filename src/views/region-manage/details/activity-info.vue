<template>
  <page-container :title="`${route.meta.title} `">
    <template #content>

      <a-descriptions :title="'当前版本：'+activityVersion ">
      </a-descriptions>

      <a-table :columns="columns" :data-source="data" ></a-table>
    </template>
  </page-container>
</template>
<script lang="ts" setup>
import { SmileOutlined, DownOutlined } from '@ant-design/icons-vue';
import { onMounted, ref } from 'vue';
import { getRegionActivityApi } from '@/api/sfmanage';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();
const currentRegion = ref();
const data = ref([]);
const activityVersion = ref();
const spinning = ref<boolean>(true);

onMounted(async () => {
  const activity = await getRegionActivityApi(currentRegion.value);
  data.value = activity.activity_data;
  activityVersion.value = activity.log_id;
});
const columns = [
  {
    title: '活动名称',
    dataIndex: 'activity_annotation',
    key: 'activity_annotation',
  },
  {
    title: '开始时间',
    dataIndex: 'activity_start_time',
    key: 'activity_start_time',
  },
  {
    title: '结束时间',
    dataIndex: 'activity_end_time',
    key: 'activity_end_time',
  },
  {
    title: 'activity_ids',
    dataIndex: 'activity_ids',
    key: 'activity_ids',
  },
  {
    title: 'activity_release_id',
    dataIndex: 'activity_release_id',
    key: 'activity_release_id',
  },
  {
    title: 'condition_ids',
    dataIndex: 'condition_ids',
    key: 'condition_ids',
  },

];

// 监听路由变化，更新选中状态
watch(
  () => route.path,
  newPath => {
    // 从路径中提取区域和功能
    const [, , region, ...funcs] = newPath.split('/');
    if (region) {
      currentRegion.value = region; // 更新全局区域变量
    }
  },
  { immediate: true },
);
</script>
