<template>
  <a-row style="height: calc(100vh - 48px)">
    <!-- 第二列菜单 - 功能菜单 -->
    <a-col :span="2">
      <a-layout-sider width="200" style="background: #fff; height: 100%">
        <a-menu
          v-model:openKeys="functionOpenKeys"
          v-model:selectedKeys="functionSelectedKeys"
          mode="inline"
          :items="functionItems"
          @click="handleFunctionClick"
        />
      </a-layout-sider>
    </a-col>

    <!-- 内容区域 -->
    <a-col :span="22" style="padding-left: 5px">
      <router-view :key="$route.fullPath"></router-view>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, h } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { MailOutlined, AppstoreOutlined, SettingOutlined } from '@ant-design/icons-vue';
import type { MenuProps } from 'ant-design-vue';

const router = useRouter();
const route = useRoute();

// 当前选中的区域（全局变量）
const currentRegion = ref<string>('');

// 区域菜单状态
const regionSelectedKeys = ref<string[]>([]);

// 功能菜单状态
const functionOpenKeys = ref<string[]>(['details', 'logs', 'settings']);
const functionSelectedKeys = ref<string[]>(['dashboard']);

// 功能菜单项
const functionItems = reactive([
  {
    key: 'dashboard',
    label: 'Dashboard',
    icon: () => h(MailOutlined),
  },
  {
    key: 'details',
    label: '详情',
    icon: () => h(AppstoreOutlined),
    children: [
      // { key: 'basic-info', label: '基础信息' },
      { key: 'activity-info', label: '活动信息' },
      // { key: 'version-info', label: '版本信息' },
    ],
  },
  // {
  //   key: 'logs',
  //   label: '日志',
  //   icon: () => h(AppstoreOutlined),
  //   children: [
  //     { key: 'business-log', label: '业务日志' },
  //     {
  //       key: 'load-balance',
  //       label: '负载均衡',
  //       children: [
  //         { key: 'dp-level-1', label: '一级DP' },
  //         { key: 'dp-level-2', label: '二级DP' },
  //       ],
  //     },
  //   ],
  // },
  // { type: 'divider' },
  // {
  //   key: 'settings',
  //   label: '设置',
  //   icon: () => h(SettingOutlined),
  //   children: [
  //     { key: 'client-publish', label: '客户端发布' },
  //     { key: 'server-values', label: '服务端数值' },
  //     { key: 'option-11', label: 'Option 11' },
  //     { key: 'option-12', label: 'Option 12' },
  //   ],
  // },
]);

// 获取菜单路径
const getMenuPath = (key: string) => {
  const findPath = (items: any[], targetKey: string, path: string[] = []): string[] | null => {
    for (const item of items) {
      if (item.key === targetKey) {
        return [...path, item.key];
      }
      if (item.children) {
        const result = findPath(item.children, targetKey, [...path, item.key]);
        if (result) return result;
      }
    }
    return null;
  };
  return findPath(functionItems, key) || [key, key];
};

// 监听路由变化，更新选中状态
watch(
  () => route.path,
  newPath => {
    // 从路径中提取区域和功能
    const [, , region, ...funcs] = newPath.split('/');
    if (region) {
      currentRegion.value = region; // 更新全局区域变量
      regionSelectedKeys.value = [region];

      // 如果没有功能路径，默认选中dashboard
      if (funcs.length === 0) {
        functionSelectedKeys.value = ['dashboard'];
        router.push(`/region-manage/${region}/dashboard`);
        return;
      }
    }

    if (funcs.length > 0) {
      // 更新选中的菜单项
      functionSelectedKeys.value = [funcs[funcs.length - 1]];
      // 更新展开的菜单项
      const parentKeys = funcs.slice(0, -1);
      if (parentKeys.length > 0) {
        functionOpenKeys.value = [...new Set([...functionOpenKeys.value, ...parentKeys])];
      }
    }
  },
  { immediate: true },
);

// 处理功能菜单点击
const handleFunctionClick: MenuProps['onClick'] = e => {
  if (!currentRegion.value) {
    message.warning('请先选择区域');
    return;
  }

  const menuPath = getMenuPath(e.key as string);
  const routePath = `/region-manage/${currentRegion.value}/${
    menuPath[1] ? menuPath[1] : menuPath[0]
  }`;
  router.push(routePath);
};
</script>

<style scoped>
.ant-layout-sider-children {
  background: #fff;
}

:deep(.ant-menu) {
  height: 100%;
}

:deep(.ant-menu-inline) {
  border-right: none;
}
</style>
