<template>
  <a-row :gutter="24">
    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :xxl="8">
      <a-card hoverable title="操作" style="margin-right: 20px">
        <a-form
          ref="accountFormRef"
          name="customized_form_controls"
          layout="vertical"
          :model="formState"
          @finish="createCopyAccountHistory"
          @finishFailed="onFinishFailed"
        >
          <a-divider>源账号</a-divider>
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item
                name="src_region_env"
                has-feedback
                :rules="[{ required: true, message: '你必须选择一个源区服!' }]"
              >
                <a-select
                  v-model:value="formState.src_region_env"
                  placeholder="请选择一个源 区服"
                  style="width: 100%"
                  show-search
                  :options="srcRegionOptions"
                ></a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item>
                <a-select v-model:value="formState.src_account_type" style="width: 100%">
                  <a-select-option value="0_uid">UID</a-select-option>
                  <a-select-option value="0_aid">游客AID</a-select-option>
                  <a-select-option value="1_aid">通行证AID</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                v-if="formState.src_account_type === '0_uid'"
                name="src_account_id"
                :rules="[
                  { required: true, message: 'aid或uid为必填项!' },
                  { pattern: /^[0-9]+$/, message: 'uid必须为纯数字!' },
                ]"
              >
                <a-input
                  v-model:value="formState.src_account_id"
                  style="width: 100%"
                  placeholder="请输入aid/uid"
                ></a-input>
              </a-form-item>
              <a-form-item
                v-else
                name="src_account_id"
                :rules="[{ required: true, message: 'aid或uid为必填项!' }]"
              >
                <a-input
                  v-model:value="formState.src_account_id"
                  style="width: 100%"
                  placeholder="请输入aid/uid"
                ></a-input>
              </a-form-item>
            </a-col>

            <a-divider>目标账号</a-divider>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item
                name="dst_region_env"
                has-feedback
                :rules="[{ required: true, message: '你必须选择一个目标区服!' }]"
              >
                <a-select
                  v-model:value="formState.dst_region_env"
                  placeholder="请选择一个目标 区服"
                  show-search
                  :options="dstRegionOptions"
                  style="width: calc(100% - 20px)"
                  @change="handleChangeDstRegion"
                />
                <a-tooltip>
                  <template #title>
                    需要申请目标区服的【拷贝账号】权限,
                    <a href="/perm/perm-apply">去申请</a>
                  </template>
                  <question-circle-outlined style="margin-left: 5px" />
                </a-tooltip>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item>
                <a-select v-model:value="formState.dst_account_type">
                  <a-select-option value="0_uid">UID</a-select-option>
                  <a-select-option value="0_aid">游客AID</a-select-option>
                  <a-select-option value="1_aid">通行证AID</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                v-if="formState.dst_account_type === '0_uid'"
                name="dst_account_id"
                :rules="[
                  { required: true, message: 'aid或uid为必填项!' },
                  { pattern: /^[0-9]+$/, message: 'uid必须为纯数字!' },
                ]"
              >
                <a-input
                  v-model:value="formState.dst_account_id"
                  placeholder="请输入aid/uid"
                ></a-input>
              </a-form-item>
              <a-form-item
                v-else
                name="dst_account_id"
                :rules="[{ required: true, message: 'aid或uid为必填项!' }]"
              >
                <a-input
                  v-model:value="formState.dst_account_id"
                  placeholder="请输入aid/uid"
                ></a-input>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item>
            <a-row>
              <a-col :span="24" style="text-align: right; padding: 20px">
                <a-space>
                  <a-checkbox
                    v-if="userState.getUser.user_name == regionDetail.owner"
                    v-model:checked="formState.kick_dst_player"
                  >
                    拷贝之前，强制用户下线
                    <span style="color: red">（仅限自己区服）</span>
                  </a-checkbox>
                  <a-button style="margin-right: 10px" @click="resetDefaultFields">重置</a-button>
                  <a-button type="primary" html-type="submit" :loading="copy_loading">
                    开始拷贝
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form-item>
        </a-form>
      </a-card>
    </a-col>
    <a-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" :xxl="16">
      <a-card hoverable title="历史记录">
        <template #extra>
          <a-switch
            v-model:checked="findAllData"
            checked-children="所有"
            un-checked-children="自己"
            style="margin-right: 20px"
            @change="current = 1"
          />
          <a-button @click="refreshCopyAccountTable()">刷新</a-button>
        </template>

        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          table-layout="auto"
          @change="handleTableChange"
        >
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'detail'">
              <a-button size="small" type="text" @click="copyText(record.src_account_id)">{{ record.src_account_id }}</a-button>
              (
              <a-button size="small" type="text" @click="copyText(record.src_region)">{{ record.src_region }}</a-button>
              / {{ record.src_account_type }} ) --->
              <a-button size="small" type="text" @click="copyText(record.dst_account_id)">{{ record.dst_account_id }}</a-button>
              (
              <a-button size="small" type="text" @click="copyText(record.dst_region)">{{ record.dst_region }}</a-button>
              / {{ record.dst_account_type }} )
            </template>
            <template v-else-if="column.dataIndex === 'create_time'">
              {{ formatTime(record.create_time) }}
            </template>
            <template v-else-if="column.dataIndex === 'status'">
              <a-tag v-if="record.status === 'success'" color="green" :title="record.create_time">
                成功
              </a-tag>
              <a-tag
                v-else-if="record.status === 'running'"
                color="blue"
                :title="record.create_time"
              >
                执行中
              </a-tag>
              <a-tag v-else color="red" :title="record.create_time">失败</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-typography-link v-if="record.status !== 'running'" @click="reCopyAccount(record)">
                再次拷贝
              </a-typography-link>
              <a-typography-link
                v-if="record.status == 'fail'"
                @click="showLogDrawer(record.dst_region, record.task_id)"
              >
                日志
              </a-typography-link>
            </template>
          </template>
        </a-table>
      </a-card>
      <a-drawer v-model:visible="logVisible" title="错误日志" placement="bottom" :height="450">
        <a-spin tip="Loading..." :spinning="logLoading">
          <a-descriptions title="">
            <a-descriptions-item label="拷贝详情">
              {{ logDetail.src_account_id }} ( {{ logDetail.src_region }} /
              {{ logDetail.src_account_type }} ) ---> {{ logDetail.dst_account_id }} (
              {{ logDetail.dst_region }} / {{ logDetail.dst_account_type }} )
            </a-descriptions-item>
            <a-descriptions-item label="操作人">{{ logDetail.operator }}</a-descriptions-item>
            <a-descriptions-item label="状态">
              <a-tag
                v-if="logDetail.status === 'success'"
                color="green"
                :title="logDetail.create_time"
              >
                成功
              </a-tag>
              <a-tag
                v-else-if="logDetail.status === 'running'"
                color="blue"
                :title="logDetail.create_time"
              >
                执行中
              </a-tag>
              <a-tag v-else color="red" :title="logDetail.create_time">失败</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(logDetail.create_time) }}
            </a-descriptions-item>
          </a-descriptions>
          <MonacoEditor v-model="logData" read-only width="100%" height="260px" />
        </a-spin>
      </a-drawer>
    </a-col>
  </a-row>
</template>
<script lang="ts" setup>
import { computed, defineComponent, onBeforeMount, reactive, ref } from 'vue';
import MonacoEditor from '@/components/monaco/monaco.vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import {type FormInstance, message, type SelectProps, type TableProps} from 'ant-design-vue';
import { usePagination } from 'vue-request';
import {
  createCopyAccountHistoryApi,
  getCopyAccountHistoryApi,
  getCopyAccountLogApi,
  getCopyAccountRegionApi,
  getRegionDetailApi,
} from '@/api/sfmanage';
import dayjs from 'dayjs';
import { useUserStore } from '@/stores/modules/user';

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '拷贝详情',
    dataIndex: 'detail',
    width: '50%',
  },
  {
    title: '操作人',
    dataIndex: 'operator',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];
// const loading = ref<boolean>(false);
const visible = ref<boolean>(false);
const copy_loading = ref<boolean>(false);
const findAllData = ref<boolean>(false);
const userState = useUserStore();

const regionDetail = ref({});

const route = useRoute(); // 获取当前路由
const router = useRouter();

interface FormState {
  src_region_env: any;
  src_account_type: string;
  src_account_channel: string;
  src_account_id: string;
  dst_region_env: any;
  dst_account_type: string;
  dst_account_channel: string;
  dst_account_id: string;
  count: number;
  kick_dst_player: boolean;
}

const formState = reactive<FormState>({
  src_region_env: null,
  src_account_type: '0_uid',
  src_account_channel: '0',
  src_account_id: '',
  dst_region_env: null,
  dst_account_type: '0_uid',
  dst_account_channel: '0',
  dst_account_id: '',
  count: 0,
  kick_dst_player: false,
});
const accountFormRef = ref<FormInstance>();

const srcRegionOptions = ref<SelectProps['options']>([]);

const dstRegionOptions = ref<SelectProps['options']>([]);
type APIParams = {
  results: number;
  page?: number;
  sortField?: string;
  sortOrder?: number;
  [key: string]: any;
};

const getCopyAccountHistory = (params: APIParams) => {
  params.all = findAllData.value;
  return getCopyAccountHistoryApi(params);
};

const {
  data: rawDataSource,
  run,
  loading,
  current,
  pageSize,
  total,
} = usePagination(getCopyAccountHistory, {
  pagination: {
    currentKey: 'page_num',
    pageSizeKey: 'page_size',
    totalKey: 'total',
  },
});

const handleOk = () => {
  loading.value = false;
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};
const formatTime = (timeStr: string) => {
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss');
};

onBeforeMount(() => {
  queryCopyAccountRegion();
});
onMounted(() => {
  const task_id = route.query.task_id as string;
  const task_region = route.query.region as string;
  if (!task_id || !task_region) {
    return;
  }
  router.replace({ query: {} });
  showLogDrawer(task_region, task_id);
});
const refreshCopyAccountTable = async () => {
  current.value = 1;
};

const dataSource = computed(() => {
  return rawDataSource.value?.list || [];
});

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
}));

const handleTableChange: TableProps['onChange'] = (
  pag: { pageSize: number; current: number },
  filters: any,
  sorter: any,
) => {
  // console.log('Various parameters', pagination, filters, sorter);
  run({
    page_size: pag.pageSize!,
    page_num: pag?.current,
    sortField: sorter.field,
    sortOrder: sorter.order,
    ...filters,
  });
};
const queryCopyAccountRegion = () => {
  getCopyAccountRegionApi().then(data => {
    srcRegionOptions.value = [
      ...data.srcRegion.map((srcRegion, _) => ({
        value: srcRegion.region + ',' + srcRegion.env,
        label: srcRegion.region,
      })),
    ];
    dstRegionOptions.value = [
      ...data.dstRegion.map((dstRegion, _) => ({
        value: dstRegion.region + ',' + dstRegion.env,
        label: dstRegion.region,
      })),
    ];
    // console.log('destRegionOptions.value: ', dstRegionOptions.value);
    const params = localStorage.getItem(lastAccount);
    if (params) {
      Object.assign(formState, JSON.parse(params));
      getRegionDetail();
    }
    // console.log('formState: ', formState);
  });
};
const lastAccount = 'LAST_ACCOUNT';
const createCopyAccountHistory = (values: any) => {
  copy_loading.value = true;
  // console.log('values: ', values, ' copy_loading:', copy_loading.value);
  const src_account_type = formState.src_account_type.split('_');
  const dst_account_type = formState.dst_account_type.split('_');
  const src_region_env = formState.src_region_env.split(',');
  const dst_region_env = formState.dst_region_env.split(',');
  // console.log(
  //   formState.src_account_type,
  //   src_account_type,
  //   formState.dst_account_type,
  //   dst_account_type,
  //   src_region_env,
  //   dst_region_env,
  // );
  const params = {
    src_region: src_region_env[0],
    src_env: src_region_env[1],
    src_account_type: src_account_type[1],
    src_account_channel: src_account_type[0],
    src_account_id: formState.src_account_id,
    dst_region: dst_region_env[0],
    dst_env: dst_region_env[1],
    dst_account_type: dst_account_type[1],
    dst_account_channel: dst_account_type[0],
    dst_account_id: formState.dst_account_id,
    kick_dst_player: formState.kick_dst_player,
  };
  console.log(params);
  createCopyAccountHistoryApi(params).then(data => {
    // console.log(data);
    copy_loading.value = false;
    // 账号拷贝请求发送成功了后，才进行存储
    localStorage.setItem(lastAccount, JSON.stringify(formState));
    current.value = 1; // 需要刷新页面
  });
};

function reCopyAccount(record) {
  // console.log(record);

  const params = {
    src_region: record.src_region,
    src_env: record.src_env,
    src_account_type: record.src_account_type,
    src_account_channel: record.src_account_channel,
    src_account_id: record.src_account_id,
    dst_region: record.dst_region,
    dst_env: record.dst_env,
    dst_account_type: record.dst_account_type,
    dst_account_channel: record.dst_account_channel,
    dst_account_id: record.dst_account_id,
  };
  // console.log(params);
  createCopyAccountHistoryApi(params).then(data => {
    // console.log(data);
  });
}

const logVisible = ref<boolean>(false);
const logData = ref<string>();
const logDetail = ref({
  src_region: '',
  src_env: '',
  src_account_type: '',
  src_account_channel: '',
  src_account_id: '',
  dst_region: '',
  dst_env: '',
  dst_account_type: '',
  dst_account_channel: '',
  dst_account_id: '',
  create_time: '',
  status: '',
  operator: '',
});
const logLoading = ref<boolean>(false);
const showLogDrawer = (region: string, taskId: string) => {
  logData.value = '';
  logVisible.value = true;
  logLoading.value = true;
  getCopyAccountLogApi(region, taskId).then(data => {
    logDetail.value = data;
    for (let row of data.job_data.result) {
      logData.value += row.explain + '\n';
    }
    logLoading.value = false;
  });
};

const resetDefaultFields = () => {
  // console.log('resetDefaultFields');
  Object.assign(formState, {
    src_region_env: null,
    src_account_type: '0_uid',
    src_account_channel: '0',
    src_account_id: '',
    dst_region_env: null,
    dst_account_type: '0_uid',
    dst_account_channel: '0',
    dst_account_id: '',
    count: 0,
  });
  localStorage.removeItem(lastAccount);
};
const onFinishFailed = (errorInfo: any) => {
  // console.log('Failed:', errorInfo);
};
const getRegionDetail = () => {
  console.log(formState);
  if (formState.dst_region_env) {
    const src_region_env = formState.dst_region_env.split(',');
    const regionName = src_region_env[0];
    getRegionDetailApi(regionName).then(data => {
      regionDetail.value = data;
    });
  }
};

const handleChangeDstRegion = () => {
  formState.kick_dst_player = false;
  getRegionDetail();
};


const copyText = (content: string) => {
    // http地址无法使用navigator.clipboard API
    const textarea = document.createElement('textarea');
    textarea.value = content;
    textarea.style.position = 'fixed'; // 防止滚动条出现
    document.body.appendChild(textarea);
    textarea.focus();
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        message.success('内容已复制到剪贴板：'+ content);
      } else {
        message.error('无法复制内容，请手动复制。');
      }
    } catch (err) {
      console.error('无法复制内容:', err);
      message.error('无法复制内容，请手动复制。');
    }
    document.body.removeChild(textarea);
};
</script>
