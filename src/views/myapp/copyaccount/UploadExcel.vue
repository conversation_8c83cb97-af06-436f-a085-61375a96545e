<template>
  <a-row>
    <a-col :span="8">
      <a-card hoverable style="margin-right: 20px">
        <a-space direction="vertical">
          <a-form :model="formState" name="horizontal_login" layout="inline" autocomplete="off">
            <a-form-item label="源账户类型" name="source_account_type">
              <a-select
                v-model:value="formState.source_account_type"
                style="width: 150px"
                placeholder="please select your zone"
              >
                <a-select-option value="source_uid">UID</a-select-option>
                <a-select-option value="source_aid">通行证AID</a-select-option>
                <a-select-option value="source_guest_aid">游客AID</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="目标账户类型" name="target_account_type">
              <a-select
                v-model:value="formState.target_account_type"
                style="width: 150px"
                placeholder="please select your zone"
              >
                <a-select-option value="target_uid">UID</a-select-option>
                <a-select-option value="target_aid">通行证AID</a-select-option>
                <a-select-option value="target_guest_aid">游客AID</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a
                type="primary"
                :href="`/api/v1/manager/tools/copy_account_download?source_account_type=${formState.source_account_type}&target_account_type=${formState.target_account_type}`"
              >
                下载模版
                <DownloadOutlined />
              </a>
              <a-tooltip title="模板下载后无法使用解决方案">
                <a
                  href="https://km.mihoyo.com/articleBase/183/149774"
                  target="_blank"
                  style="margin-left: 5px"
                >
                  <QuestionCircleOutlined />
                </a>
              </a-tooltip>
            </a-form-item>
          </a-form>

          <a-divider></a-divider>
          <a-alert
            message="你可以先选择需要拷贝的源账户类型/目标账号类型，然后下载一份模版，填充数据后进行上传，最后点击开始拷贝"
            type="warning"
          />

          <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            :multiple="false"
            action="/api/v1/manager/tools/copy_account_upload"
            :headers="headers"
            :max-count="1"
            accept=".xls,.xlsx"
            :before-upload="beforeUpload"
            @change="handleChange"
            @drop="handleDrop"
            @remove="handleRemove"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined></inbox-outlined>
            </p>
            <p class="ant-upload-text">Click or drag file to this area to upload</p>
            <p class="ant-upload-hint">你仅可以上传一个Excel文件，进行批量拷贝账号</p>
          </a-upload-dragger>
          <a-button
            type="primary"
            style=""
            :disabled="btnCopyDisabled"
            :loading="start_copy_loading"
            @click="startBatchCopyAccount"
          >
            开始拷贝
          </a-button>
          <a-table :columns="columns" :data-source="data"></a-table>
        </a-space>
      </a-card>
    </a-col>
    <a-col :span="16">
      <a-card hoverable :loading="loading" title="历史记录">
        <template #extra>
          <a-switch
            v-model:checked="findAllData"
            checked-children="所有"
            un-checked-children="自己"
            style="margin-right: 20px"
            @change="current = 1"
          />
          <a-button @click="refreshCopyAccountTable()">刷新</a-button>
        </template>

        <a-table
          :columns="historyColumns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          table-layout="auto"
          @change="handleTableChange"
        >
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'detail'">
              {{ record.src_account_id }} ( {{ record.src_region }} /
              {{ record.src_account_type }} ) ---> {{ record.dst_account_id }} (
              {{ record.dst_region }} / {{ record.dst_account_type }} )
            </template>
            <template v-else-if="column.dataIndex === 'create_time'">
              {{ formatTime(record.create_time) }}
            </template>
            <template v-else-if="column.dataIndex === 'status'">
              <a-tag v-if="record.status === 'success'" color="green" :title="record.create_time">
                成功
              </a-tag>
              <a-tag
                v-else-if="record.status === 'running'"
                color="blue"
                :title="record.create_time"
              >
                执行中
              </a-tag>
              <a-tag v-else color="red" :title="record.create_time">失败</a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-typography-link v-if="record.status !== 'running'" @click="reCopyAccount(record)">
                再次拷贝
              </a-typography-link>
              <a-typography-link
                v-if="record.status == 'fail'"
                @click="showLogDrawer(record.dst_region, record.task_id)"
              >
                日志
              </a-typography-link>
            </template>
          </template>
        </a-table>
      </a-card>
    </a-col>
  </a-row>
  <a-drawer v-model:visible="logVisible" title="错误日志" placement="bottom" :height="450">
    <a-spin tip="Loading..." :spinning="logLoading">
      <a-descriptions title="">
        <a-descriptions-item label="拷贝详情">
          {{ logDetail.src_account_id }} ( {{ logDetail.src_region }} /
          {{ logDetail.src_account_type }} ) ---> {{ logDetail.dst_account_id }} (
          {{ logDetail.dst_region }} / {{ logDetail.dst_account_type }} )
        </a-descriptions-item>
        <a-descriptions-item label="操作人">{{ logDetail.operator }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag v-if="logDetail.status === 'success'" color="green" :title="logDetail.create_time">
            成功
          </a-tag>
          <a-tag
            v-else-if="logDetail.status === 'running'"
            color="blue"
            :title="logDetail.create_time"
          >
            执行中
          </a-tag>
          <a-tag v-else color="red" :title="logDetail.create_time">失败</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ formatTime(logDetail.create_time) }}
        </a-descriptions-item>
      </a-descriptions>
      <MonacoEditor v-model="logData" read-only width="100%" height="260px" />
    </a-spin>
  </a-drawer>
</template>
<script lang="ts" setup>
import { computed, onBeforeMount, ref } from 'vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import type { UnwrapRef } from 'vue';
import type { TableProps, UploadChangeParam, UploadProps } from 'ant-design-vue';
import { message, Upload } from 'ant-design-vue';
import { useUserStore } from '@/stores/modules/user';
import { DownloadOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import {
  batchCopyAccountHistoryApi,
  createCopyAccountHistoryApi,
  getCopyAccountHistoryApi,
  getCopyAccountLogApi,
} from '@/api/sfmanage';
import { usePagination } from 'vue-request';
import dayjs from 'dayjs';
import MonacoEditor from '@/components/monaco/monaco.vue';

const route = useRoute(); // 获取当前路由
const router = useRouter();
const userStore = useUserStore();
const token = userStore.getToken;
const headers = {
  authorization: token,
};

interface FormState {
  source_account_type: string;
  target_account_type: string;
}

const fileList = ref([]);
const btnCopyDisabled = ref(true);
const start_copy_loading = ref(false);

const formState: UnwrapRef<FormState> = reactive({
  source_account_type: 'source_uid',
  target_account_type: 'target_aid',
});
const columns = ref([]);
const data = ref([]);
const loading = ref(false);
const tableData = ref();
const logVisible = ref<boolean>(false);
const findAllData = ref<boolean>(false);
const handleChange = (info: UploadChangeParam) => {
  // console.log('info.file.status: ', info.file.status);
  // console.log('UploadChangeParam: ', info);
  // console.log('fileList.value: ', fileList.value);

  const status = info.file.status;
  if (status !== 'uploading') {
    // console.log('uploading', info.file, info.fileList);
  } else {
    // fileList.value = [];
  }

  // if (info.fileList.length > 1) {
  //   message.error(`${info.file.name} 不能上传，你最多只能上传一个文件.`);
  // }
  if (status === 'done') {
    message.success(`${info.file.name} file uploaded successfully.`, 5);
    columns.value = info.file.response.data.columns;
    data.value = info.file.response.data.data_source;
    tableData.value = info.file.response.data;
    // console.log('done! fileList.value: ', fileList.value);
    btnCopyDisabled.value = false;
  } else if (status === 'error') {
    message.error(
      `${info.file.name} file upload failed. message: ${info.file.response['message']}`,
      5,
    );
  }
};

function handleDrop(e: DragEvent) {
  // console.log('handleDrop', e);
}

function handleRemove(file: never) {
  // console.log('fileList: ', file);
  btnCopyDisabled.value = true;
  fileList.value = [];
  data.value = [];
}

const beforeUpload: UploadProps['beforeUpload'] = file => {
  // 在上传前，确保只上传最新的文件
  if (fileList.value.length > 0) {
    // 移除当前的文件
    fileList.value = [];
  }
  // console.log(file.type);
  const isExcel = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
    'application/vnd.ms-excel.addin.macroEnabled.12',
    'application/vnd.ms-excel.template.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
  ].includes(file.type);
  // const isExcel = true;
  if (!isExcel) {
    message.error(`${file.name} is not a Excel file`);
  }
  return isExcel || Upload.LIST_IGNORE;
};

function startBatchCopyAccount() {
  start_copy_loading.value = true;
  batchCopyAccountHistoryApi(tableData.value).then(data => {
    // console.log(data);
    start_copy_loading.value = false;
    current.value = 1; // 刷新列表
  });
}

type APIParams = {
  results: number;
  page?: number;
  sortField?: string;
  sortOrder?: number;
  [key: string]: any;
};
const getCopyAccountHistory = (params: APIParams) => {
  params.is_batch = true;
  params.all = findAllData.value;
  return getCopyAccountHistoryApi(params);
};

const {
  data: rawDataSource,
  run,
  // loading,
  current,
  pageSize,
  total,
} = usePagination(getCopyAccountHistory, {
  pagination: {
    currentKey: 'page_num',
    pageSizeKey: 'page_size',
    totalKey: 'total',
  },
});
const historyColumns = ref([
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '拷贝详情',
    dataIndex: 'detail',
    width: '45%',
  },
  {
    title: '操作人',
    dataIndex: 'operator',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
  },
  {
    title: '批处理ID',
    dataIndex: 'batch_uuid',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
]);

onBeforeMount(() => {
  // console.log(`the component is now mounted.`);
});

onMounted(() => {
  const task_id = route.query.task_id as string;
  const task_region = route.query.region as string;
  if (!task_id || !task_region) {
    return;
  }
  router.replace({ query: {} });
  showLogDrawer(task_region, task_id);
});
const refreshCopyAccountTable = async () => {
  current.value = 1;
};
const formatTime = (timeStr: string) => {
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm:ss');
};
const dataSource = computed(() => {
  return rawDataSource.value?.list || [];
});

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
}));

const handleTableChange: TableProps['onChange'] = (
  pag: { pageSize: number; current: number },
  filters: any,
  sorter: any,
) => {
  // console.log('Various parameters', pagination, filters, sorter);
  run({
    page_size: pag.pageSize!,
    page_num: pag?.current,
    sortField: sorter.field,
    sortOrder: sorter.order,
    is_batch: true,
    ...filters,
  });
};

function reCopyAccount(record) {
  // console.log(record);

  const params = {
    src_region: record.src_region,
    src_env: record.src_env,
    src_account_type: record.src_account_type,
    src_account_channel: record.src_account_channel,
    src_account_id: record.src_account_id,
    dst_region: record.dst_region,
    dst_env: record.dst_env,
    dst_account_type: record.dst_account_type,
    dst_account_channel: record.dst_account_channel,
    dst_account_id: record.dst_account_id,
  };
  // console.log(params);
  createCopyAccountHistoryApi(params).then(data => {
    // console.log(data);
  });
}
const logData = ref<string>();
const logDetail = ref({
  src_region: '',
  src_env: '',
  src_account_type: '',
  src_account_channel: '',
  src_account_id: '',
  dst_region: '',
  dst_env: '',
  dst_account_type: '',
  dst_account_channel: '',
  dst_account_id: '',
  create_time: '',
  status: '',
  operator: '',
});
const logLoading = ref<boolean>(false);
const showLogDrawer = (region: string, taskId: string) => {
  logData.value = '';
  logVisible.value = true;
  logLoading.value = true;
  getCopyAccountLogApi(region, taskId).then(data => {
    logDetail.value = data;
    for (let row of data.job_data.result) {
      logData.value += row.explain + '\n';
    }
    logLoading.value = false;
  });
};
</script>
