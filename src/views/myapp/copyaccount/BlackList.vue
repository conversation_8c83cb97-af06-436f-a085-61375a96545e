<template>
  <a-row>
    <a-col span="22">
      <a-form layout="inline" :model="searchForm" :loading="loading" @finish="getBlackList">
        <a-form-item label="区服" name="dst_region">
          <a-input v-model:value="searchForm.dst_region" placeholder="目标区服名"></a-input>
        </a-form-item>
        <a-form-item label="账号ID" name="dst_account_id">
          <a-input
            v-model:value="searchForm.dst_account_id"
            placeholder="目标账号AID或UID"
          ></a-input>
        </a-form-item>
        <a-form-item label="创建人" name="operator">
          <a-input v-model:value="searchForm.operator" placeholder="黑名单创建人"></a-input>
        </a-form-item>
        <a-form-item label="只看生效中" name="valid">
          <a-switch
            :checked="searchForm.valid"
            @change="() => (searchForm.valid = !searchForm.valid)"
          ></a-switch>
        </a-form-item>
        <a-button type="primary" html-type="submit" :loading="loading">查询</a-button>
      </a-form>
    </a-col>
    <a-col>
      <a-button
        type="primary"
        @click="
          openModal = true;
          queryCopyAccountRegion();
        "
      >
        新增
      </a-button>
    </a-col>
  </a-row>
  <a-table
    style="margin-top: 10px"
    :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
    :columns="columns"
    :data-source="dataSource"
    :pagination="pagination"
    :row-key="'id'"
    @change="tableChange"
  >
    <template #footer>
      <div style="margin-bottom: 16px">
        <a-button
          type="primary"
          :disabled="!hasSelected"
          danger
          :loading="loading"
          @click="batchDeleteBlackList"
        >
          删除
        </a-button>
        <span style="margin-left: 8px">
          <template v-if="hasSelected">
            {{ `已选择 ${state.selectedRowKeys.length} 项` }}
          </template>
        </span>
      </div>
    </template>
  </a-table>
  <a-modal :open="openModal" title="新增拷号黑名单" :footer="null" @cancel="openModal = false">
    <a-form :model="createForm" layout="vertical" @finish="batchCreateBlackList">
      <a-form-item
        label="目标区服"
        name="dst_region"
        :rules="[{ required: true, message: '请选择区服', trigger: 'blur' }]"
      >
        <a-select
          v-model:value="createForm.dst_region"
          :autofocus="true"
          placeholder="目标区服"
          show-search
          :options="dstRegionOptions"
        />
      </a-form-item>
      <a-form-item
        label="目标账号类型"
        name="dst_account_type"
        :rules="[{ required: true, message: '请选择目标账号类型', trigger: 'blur' }]"
      >
        <a-select v-model:value="createForm.dst_account_type">
          <a-select-option value="uid">UID</a-select-option>
          <a-select-option value="aid">AID</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        label="目标账号ID(每行一个)"
        name="dst_account_ids"
        :rules="[
          { required: true, message: '请输入目标账号ID', trigger: 'blur' },
          { min: 1, message: '目标账号ID不能为空', trigger: 'blur' },
        ]"
      >
        <a-textarea
          v-model:value="createForm.dst_account_ids"
          :auto-size="{ minRows: 5 }"
          placeholder="UID1&#10;UID2&#10;......"
        ></a-textarea>
      </a-form-item>
      <a-form-item
        label="禁止拷号时间段"
        name="black_time_range"
        :rules="[{ required: true, message: '请选择禁止拷号时间段', trigger: 'blur' }]"
      >
        <a-range-picker v-model:value="createForm.black_time_range" show-time></a-range-picker>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input v-model:value="createForm.remark" aria-placeholder=""></a-input>
      </a-form-item>
      <a-button type="primary" html-type="submit" :loading="loading">提交</a-button>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import {
  batchCreateCopyAccountBlackListApi,
  batchDeleteCopyAccountBlackListApi,
  getCopyAccountBlackListApi,
  getCopyAccountRegionApi,
} from '@/api/sfmanage';
import type {
  CopyAccountBlackList,
  CopyAccountBlackListReq,
  CreateCopyAccountBlackList,
  DeleteCopyAccountBlackList,
} from '@/api/model';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { ref } from 'vue';
import { SelectProps } from 'ant-design-vue';

type createBlackListForm = {
  dst_region: string;
  dst_account_type: string;
  dst_account_ids: string;
  black_time_range: [Dayjs, Dayjs];
  remark: string;
};

const loading = ref<boolean>(false);

const searchForm = ref<CopyAccountBlackListReq>({
  dst_region: '',
  dst_account_id: '',
  operator: '',
  valid: true,
  page_num: 1,
  page_size: 15,
});

const createForm = ref<createBlackListForm>({
  dst_region: '',
  dst_account_type: 'uid',
  dst_account_ids: '',
  black_time_range: [dayjs(), dayjs().add(1, 'day')],
  remark: '',
});

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: '区服',
    dataIndex: 'dst_region',
  },
  {
    title: '账号类型',
    dataIndex: 'dst_account_type',
  },
  {
    title: '账号ID',
    dataIndex: 'dst_account_id',
  },
  {
    title: '锁定开始',
    dataIndex: 'begin_time',
  },
  {
    title: '锁定结束',
    dataIndex: 'end_time',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    dataIndex: 'operator',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
  },
];

const pagination = ref({
  current: 1,
  pageSize: 15,
  total: 0,
});

const dataSource = ref<CopyAccountBlackList[]>([]);

const state = reactive<{
  selectedRowKeys: number[];
  loading: boolean;
}>({
  selectedRowKeys: [],
  loading: false,
});
const hasSelected = computed(() => state.selectedRowKeys.length > 0);

const openModal = ref<boolean>(false);

const dstRegionOptions = ref<SelectProps['options']>([]);

onMounted(async () => {
  await getBlackList();
});

async function getBlackList() {
  const params = {
    dst_region: searchForm.value.dst_region,
    dst_account_id: searchForm.value.dst_account_id,
    operator: searchForm.value.operator,
    valid: searchForm.value.valid,
    page_num: searchForm.value.page_num,
    page_size: searchForm.value.page_size,
  };
  loading.value = true;
  const data = await getCopyAccountBlackListApi(params);
  dataSource.value = data.items;
  pagination.value.total = data.total;
  pagination.value.current = searchForm.value.page_num;
  pagination.value.pageSize = searchForm.value.page_size;
  loading.value = false;
}

async function tableChange(pagi, filters, sorter) {
  searchForm.value.page_size = pagi.page_size;
  searchForm.value.page_num = pagi.current;
  await getBlackList();
}

const onSelectChange = (selectedRowKeys: number[]) => {
  // console.log('selectedRowKeys changed: ', selectedRowKeys);
  state.selectedRowKeys = selectedRowKeys;
};

async function batchDeleteBlackList() {
  const body: Array<DeleteCopyAccountBlackList> = [];
  const selectedRows = dataSource.value.filter((item: { id: number }) =>
    state.selectedRowKeys.includes(item.id),
  );
  for (const i in selectedRows) {
    body.push({
      id: selectedRows[i].id,
      dst_region: selectedRows[i].dst_region,
    });
  }

  loading.value = true;
  await batchDeleteCopyAccountBlackListApi(body);
  getBlackList();
  loading.value = false;
}

function queryCopyAccountRegion() {
  getCopyAccountRegionApi().then(data => {
    dstRegionOptions.value = [
      ...data.dstRegion.map((dstRegion, _) => ({
        value: dstRegion.region,
        label: dstRegion.region,
      })),
    ];
  });
}

async function batchCreateBlackList() {
  const body: Array<CreateCopyAccountBlackList> = createForm.value.dst_account_ids
    .split('\n')
    .map(data => {
      return {
        dst_region: createForm.value.dst_region,
        dst_account_type: createForm.value.dst_account_type,
        dst_account_id: data,
        begin_time: createForm.value.black_time_range[0],
        end_time: createForm.value.black_time_range[1],
        remark: createForm.value.remark,
      };
    });

  loading.value = true;
  await batchCreateCopyAccountBlackListApi(body);
  await getBlackList();
  openModal.value = false;

  loading.value = false;
}
</script>

<style scoped></style>
