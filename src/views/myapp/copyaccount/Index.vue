<template>
  <div>
    <page-container title="区服账号拷贝">
      <template #content>
        <div style="margin-right: 10px">
          <a-tabs v-model:activeKey="activeKey" style="padding: 15px">
            <a-tab-pane key="1" tab="单账号拷贝">
              <SingleCopy></SingleCopy>
            </a-tab-pane>
            <a-tab-pane key="3" tab="批量导入账号">
              <UploadExcel></UploadExcel>
            </a-tab-pane>
            <a-tab-pane key="4" tab="拷号黑名单">
              <BlackList></BlackList>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </page-container>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import SingleCopy from '@/views/myapp/copyaccount/SingleCopy.vue';
import UploadExcel from '@/views/myapp/copyaccount/UploadExcel.vue';
import BlackList from '@/views/myapp/copyaccount/BlackList.vue';
const activeKey = ref('');
const route = useRoute(); // 获取当前路由

onMounted(() => {
  const is_batch = route.query.is_batch as string;
  if (is_batch == 'true') {
    activeKey.value = '3';
  } else {
    activeKey.value = '1';
  }
});
</script>
<!-- border-radius 第一个值为左上角，第二个值为右上角，第三个值为右下角，第四个值为左下角。-->
<style scoped></style>
