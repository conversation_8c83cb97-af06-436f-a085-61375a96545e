<template>
  <div>
    <a-row>
      <a-col span="22">
        <a-form layout="inline" :model="searchForm" :loading="loading" @finish="getHistory">
          <a-form-item>
            <a-input
              v-model:value="searchForm.search"
              allow-clear
              @change="searchForm.page = 1"
            ></a-input>
          </a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">查询</a-button>
        </a-form>
      </a-col>
      <a-col>
        <a-button type="primary" @click="modalVisible = true">新增</a-button>
      </a-col>
    </a-row>
    <a-table
      style="margin-top: 10px"
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :row-key="'id'"
      @change="tableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag v-if="record.status === 'deploy_running'" color="orange">部署中</a-tag>
          <a-tag v-else-if="record.status === 'deploy_success'" color="green">部署成功</a-tag>
          <a-popover v-else-if="record.status === 'deploy_fail'" trigger="click">
            <template #content>{{ record.msg }}</template>
            <a-tag color="red">部署失败</a-tag>
          </a-popover>
          <a-tag v-else-if="record.status === 'delete_running'" color="warning" :bordered="false">
            删除中
          </a-tag>
          <a-tag v-else-if="record.status === 'delete_success'" color="gray" :bordered="false">
            删除成功
          </a-tag>
          <a-popover v-else-if="record.status === 'delete_fail'" trigger="click">
            <template #content>{{ record.msg }}</template>
            <a-tag color="error" :bordered="false">删除失败</a-tag>
          </a-popover>
          <a-tag v-else>{{ record.status }}</a-tag>
        </template>
      </template>
    </a-table>
    <Deploy v-model:visible="modalVisible" :modal-visible="modalVisible"></Deploy>
  </div>
</template>

<script setup lang="ts">
import { getPreIFixDeployApi, deletePreIFixDeployApi } from '@/api/sfmanage';
import Deploy from '@/views/myapp/preifix/Deploy.vue';
import { ref } from 'vue';
import { DeletePreIFixDeployReq, PreIFixDeployItems } from '@/api/model';
import { Modal } from 'ant-design-vue';

const PRIVILEGE_REGION = 'live_gf01_cn'; // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制

interface SearchFrom {
  search: string;
  page: number;
  size: number;
}

const searchForm = ref<SearchFrom>({
  search: '',
  page: 1,
  size: 15,
});

const loading = ref<boolean>(false);
const modalVisible = ref<boolean>(false);

const pagination = ref({
  current: 1,
  pageSize: 15,
  total: 0,
});

const dataSource = ref<PreIFixDeployItems[]>([]);

const columns = [
  {
    title: '客户端版本号',
    dataIndex: 'client_version',
    width: 200,
  },
  {
    title: 'PreIFix',
    dataIndex: 'pre_ifix',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    width: 200,
  },
];

onMounted(async () => {
  await getHistory();
});

async function tableChange(pagi, filters, sorter) {
  searchForm.value.size = pagi.page_size;
  searchForm.value.page = pagi.current;
  await getHistory();
}

async function getHistory() {
  const params = {
    search: searchForm.value.search,
    page: searchForm.value.page,
    size: searchForm.value.size,
    privilege_region: PRIVILEGE_REGION, // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制
  };

  loading.value = true;
  const data = await getPreIFixDeployApi(params);
  dataSource.value = data.items;
  pagination.value.total = data.total;
  pagination.value.current = searchForm.value.page;
  pagination.value.pageSize = searchForm.value.size;
  loading.value = false;
}
</script>

<style scoped></style>
