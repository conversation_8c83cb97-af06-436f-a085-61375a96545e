<template>
  <div>
    <a-row>
      <a-col span="22">
        <a-form layout="inline" :model="searchForm" :loading="loading" @finish="getHistory">
          <a-form-item>
            <a-input
              v-model:value="searchForm.search"
              allow-clear
              @change="searchForm.page = 1"
            ></a-input>
          </a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">查询</a-button>
        </a-form>
      </a-col>
      <a-col>
        <a-button type="primary" @click="modalVisible = true">新增</a-button>
      </a-col>
    </a-row>
    <a-table
      style="margin-top: 10px"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :row-key="'id'"
      @change="tableChange"
    >
      <template #footer>
        <div style="margin-bottom: 16px">
          <a-button
            type="primary"
            :disabled="!hasSelected"
            danger
            :loading="loading"
            @click="confirmDeletePreIFix"
          >
            删除
          </a-button>
          <span style="margin-left: 8px">
            <template v-if="hasSelected">
              {{ `已选择 ${state.selectedRowKeys.length} 项` }}
            </template>
          </span>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag v-if="record.status === 'deploy_running'" color="orange">部署中</a-tag>
          <a-tag v-else-if="record.status === 'deploy_success'" color="green">部署成功</a-tag>
          <a-popover v-else-if="record.status === 'deploy_fail'" trigger="click">
            <template #content>{{ record.msg }}</template>
            <a-tag color="red">部署失败</a-tag>
          </a-popover>
          <a-tag v-else-if="record.status === 'delete_running'" color="warning" :bordered="false">
            删除中
          </a-tag>
          <a-tag v-else-if="record.status === 'delete_success'" color="gray" :bordered="false">
            删除成功
          </a-tag>
          <a-popover v-else-if="record.status === 'delete_fail'" trigger="click">
            <template #content>{{ record.msg }}</template>
            <a-tag color="error" :bordered="false">删除失败</a-tag>
          </a-popover>
          <a-tag v-else>{{ record.status }}</a-tag>
        </template>
      </template>
    </a-table>
    <Deploy v-model:visible="modalVisible" :modal-visible="modalVisible"></Deploy>
    <a-modal
      v-model:open="confirmDelete"
      :title="`确定删除以下记录(共${selectRows.length}条):`"
      width="80%"
      @ok="deletePreIFix"
    >
      <a-list>
        <a-list-item v-for="(row, i) in selectRows" :key="row.id">
          <a-descriptions :title="`第${i + 1}条`" size="small" :column="10">
            <a-descriptions-item label="客户端版本号" span="1">
              {{ row.client_version }}
            </a-descriptions-item>
            <a-descriptions-item label="PreIFix" span="4">
              {{ row.pre_ifix }}
            </a-descriptions-item>
            <a-descriptions-item label="当前状态" span="1">
              <a-tag v-if="row.status === 'deploy_running'" color="orange">部署中</a-tag>
              <a-tag v-else-if="row.status === 'deploy_success'" color="green">部署成功</a-tag>
              <a-tag v-else-if="row.status === 'deploy_fail'" color="red">部署失败</a-tag>
              <a-tag v-else-if="row.status === 'delete_running'" color="warning" :bordered="false">
                删除中
              </a-tag>
              <a-tag v-else-if="row.status === 'delete_success'" color="gray" :bordered="false">
                删除成功
              </a-tag>
              <a-tag v-else-if="row.status === 'delete_fail'" color="error" :bordered="false">
                删除失败
              </a-tag>
              <a-tag v-else>{{ row.status }}</a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-list-item>
      </a-list>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getCurrentPreIFixDeployApi, deletePreIFixDeployApi } from '@/api/sfmanage';
import Deploy from '@/views/myapp/preifix/Deploy.vue';
import { ref } from 'vue';
import { DeletePreIFixDeployReq, PreIFixDeployItems } from '@/api/model';
import { Modal } from 'ant-design-vue';

const PRIVILEGE_REGION = 'live_gf01_cn'; // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制

interface SearchFrom {
  search: string;
  page: number;
  size: number;
}

const searchForm = ref<SearchFrom>({
  search: '',
  page: 1,
  size: 15,
});

const loading = ref<boolean>(false);
const modalVisible = ref<boolean>(false);

const state = reactive<{
  selectedRowKeys: number[];
  loading: boolean;
}>({
  selectedRowKeys: [],
  loading: false,
});
const hasSelected = computed(() => state.selectedRowKeys.length > 0);

const pagination = ref({
  current: 1,
  pageSize: 15,
  total: 0,
});

const dataSource = ref<PreIFixDeployItems[]>([]);

const columns = [
  {
    title: '客户端版本号',
    dataIndex: 'client_version',
    width: 200,
  },
  {
    title: 'PreIFix',
    dataIndex: 'pre_ifix',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 150,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 200,
  },
  {
    title: '更新时间',
    dataIndex: 'updated_at',
    width: 200,
  },
];

const onSelectChange = (selectedRowKeys: number[]) => {
  // console.log('selectedRowKeys changed: ', selectedRowKeys);
  state.selectedRowKeys = selectedRowKeys;
};

const confirmDelete = ref<boolean>(false);
const selectRows = computed(() => {
  return dataSource.value.filter(item => state.selectedRowKeys.includes(item.id));
});

onMounted(async () => {
  await getHistory();
});

async function tableChange(pagi, filters, sorter) {
  searchForm.value.size = pagi.page_size;
  searchForm.value.page = pagi.current;
  await getHistory();
}

async function getHistory() {
  const params = {
    search: searchForm.value.search,
    page: searchForm.value.page,
    size: searchForm.value.size,
    privilege_region: PRIVILEGE_REGION, // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制
  };

  loading.value = true;
  const data = await getCurrentPreIFixDeployApi(params);
  dataSource.value = data.items;
  pagination.value.total = data.total;
  pagination.value.current = searchForm.value.page;
  pagination.value.pageSize = searchForm.value.size;
  loading.value = false;
}

async function confirmDeletePreIFix() {
  confirmDelete.value = true;
}

async function deletePreIFix() {
  Modal.confirm({
    title: '确认删除',
    content: '确定要进行删除操作？',
    style: {
      zIndex: 10000, // 设置一个比当前模态框更高的 z-index
    },
    onOk: async () => {
      const body: Array<DeletePreIFixDeployReq> = [];
      const selectedRows = dataSource.value.filter((item: { id: number }) =>
        state.selectedRowKeys.includes(item.id),
      );
      for (const i in selectedRows) {
        body.push({
          id: selectedRows[i].id,
          privilege_region: PRIVILEGE_REGION, // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制
        });
      }

      await deletePreIFixDeployApi(body);
      confirmDelete.value = false;
      await getHistory();
    },
    onCancel() {
      // console.log('cancel');
    },
  });
}
</script>

<style scoped></style>
