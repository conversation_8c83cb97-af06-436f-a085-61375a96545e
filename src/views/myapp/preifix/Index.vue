<template>
  <div>
    <page-container title="PreIFix发布">
      <template #content>
        <a-tabs v-model:activeKey="activeKey" style="padding: 15px" destroy-inactive-tab-pane>
          <a-tab-pane key="1" tab="当前版本">
            <Current></Current>
          </a-tab-pane>
          <a-tab-pane key="2" tab="历史记录">
            <History></History>
          </a-tab-pane>
        </a-tabs>
      </template>
    </page-container>
  </div>
</template>
<script lang="ts" setup>
import History from '@/views/myapp/preifix/History.vue';
import { ref } from 'vue';
import Current from '@/views/myapp/preifix/Current.vue';

const activeKey = ref('1');
</script>
<!-- border-radius 第一个值为左上角，第二个值为右上角，第三个值为右下角，第四个值为左下角。-->
<style scoped></style>
