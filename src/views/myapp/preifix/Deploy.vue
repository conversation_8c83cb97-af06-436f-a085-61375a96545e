<template>
  <a-modal :open="modalVisible" title="发布PreIFix" :footer="null" width="80%" @cancel="closeModal">
    <a-form
      ref="deployFormRef"
      name="customized_form_controls"
      :model="formState"
      @finish="createPreIFixDeploy"
    >
      <a-form-item label="地区/平台" has-feedback style="width: 80%">
        <a-tree-select
          v-model:value="formState.areaPlat"
          show-search
          style="width: 100%"
          allow-clear
          multiple
          :show-checked-strategy="SHOW_CHILD"
          tree-default-expand-all
          :tree-data="allVersionAreaPlat"
          label-in-value
          tree-checkable
          @change="versionChange"
        ></a-tree-select>
      </a-form-item>
      <a-form-item label="环境">
        <a-radio-group v-model:value="formState.env" @change="versionChange">
          <a-radio v-for="env in allVersionEnv" :key="env" :value="env">{{ env }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="版本号">
        <a-input v-model:value="formState.num" style="width: 15%" @change="versionChange"></a-input>
      </a-form-item>
      <a-form-item label="PreIFix">
        <a-select
          v-model:value="formState.url"
          show-search
          style="width: 80%"
          :options="allUrlState.data"
          @search="searchPreIFixUrl"
        >
          <template v-if="allUrlState.fetching" #notFoundContent>
            <a-spin size="small" />
          </template>
        </a-select>
      </a-form-item>
      <a-button type="primary" html-type="submit">发布</a-button>

      <a-divider />
      <a-list
        size="small"
        bordered
        style="width: 50%; margin-top: 20px"
        :grid="{ column: 5 }"
        :data-source="allClientVersion"
      >
        <template #renderItem="{ item }">
          <a-list-item>
            <a-tag color="blue">
              <b>{{ item }}</b>
            </a-tag>
          </a-list-item>
        </template>
        <template #header>
          <div>本次发布将涉及如下客户端版本号（共{{ allClientVersion.length }}个）:</div>
        </template>
      </a-list>
      <a-divider />
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { getEnumConfigsApi, getPreIFixBuildApi, createPreIFixDeployApi } from '@/api/sfmanage';
import type { TreeSelectProps } from 'ant-design-vue';
import { Modal, TreeSelect } from 'ant-design-vue';
import { onMounted } from 'vue';
import { reactive, watch } from 'vue';
import { debounce } from 'lodash';
import type { CreatePreIFixDeployReq, PreIFixDeployClientVersions } from '@/api/model';
import { message } from 'ant-design-vue';

const PRIVILEGE_REGION = 'live_gf01_cn'; // 当前权限必须依赖区服对象，此处借用live_gf01_cn实现权限控制

const SHOW_CHILD = TreeSelect.SHOW_CHILD;
const CategEnv = 'pre_ifix_client_version_env';
const CategAreaPlat = 'pre_ifix_area_platform';

const deployFormRef = ref(null);

interface urlState {
  label: string;
  value: string;
}
interface AllUrlState {
  data: urlState[];
  value: string;
  fetching: boolean;
}
const allUrlState = reactive<AllUrlState>({
  data: [],
  value: '',
  fetching: false,
});

let allVersionAreaPlat: TreeSelectProps['treeData'] = [];
let allVersionEnv: (string | number)[] = [];
let allClientVersion = ref<string[]>([]);

interface FormState {
  areaPlat: { label: string; value: string }[];
  env: string;
  num: string;
  url: string;
}

const formState = reactive<FormState>({
  areaPlat: [],
  env: '',
  num: '',
  url: '',
});

const props = defineProps({
  modalVisible: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const emit = defineEmits(['update:visible']);

onMounted(async () => {
  await getVersionEnv();
  await getVersionAreaPlat();
  await getPreIFixUrl('');
});

async function getVersionAreaPlat() {
  const respEnumAreaPlat = await getEnumConfigsApi(CategAreaPlat);
  allVersionAreaPlat = respEnumAreaPlat.items.map(item => {
    return {
      label: item.label,
      value: item.label,
      selectable: false,
      children: item.value
        .toString()
        .split(',')
        .map(c => {
          return {
            label: item.label + '-' + c,
            value: item.label + '-' + c,
          };
        }),
    };
  });
}

async function getVersionEnv() {
  const respEnumEnv = await getEnumConfigsApi(CategEnv);
  allVersionEnv = respEnumEnv.items.map(item => {
    return item.value;
  });
}

async function getPreIFixUrl(searchUrl: string) {
  const params = {
    search: searchUrl,
    page: 1,
    size: 10,
    privilege_region: PRIVILEGE_REGION,
  };

  const respUrl = await getPreIFixBuildApi(params);
  allUrlState.data = respUrl.items.map(item => {
    return {
      label: item.url,
      value: item.url,
    };
  });
}

const searchPreIFixUrl = debounce(getPreIFixUrl);

function closeModal() {
  emit('update:visible', false);
}

function versionChange() {
  allClientVersion.value = [];
  if (formState.areaPlat.length > 0 && formState.env !== '' && formState.num !== '') {
    for (const areaPlat of formState.areaPlat) {
      const area = areaPlat.value.split('-')[0];
      const plat = areaPlat.value.split('-')[1];
      allClientVersion.value.push(area + formState.env + plat + formState.num);
    }
  }
}

async function createPreIFixDeploy() {
  if (
    formState.areaPlat.length === 0 ||
    formState.env === '' ||
    formState.num === '' ||
    formState.url === ''
  ) {
    message.error('地区/平台/环境/版本号/PreIFix不能为空');
    return;
  }

  if (!/^\d+\.\d+\.\d+$/.test(formState.num)) {
    message.error('版本号格式为x.y.z（x/y/z为纯数字）');
    return;
  }

  Modal.confirm({
    title: '确认',
    content: '确定要进行创建操作？',
    onOk: async () => {
      let versions: PreIFixDeployClientVersions[] = [];
      for (const areaPlat of formState.areaPlat) {
        versions.push({
          client_version_area: areaPlat.value.split('-')[0],
          client_version_env: formState.env,
          client_version_platform: areaPlat.value.split('-')[1],
          client_version_number: formState.num,
        });
      }

      const body: CreatePreIFixDeployReq = {
        client_version_infos: versions,
        url: formState.url,
        privilege_region: PRIVILEGE_REGION,
      };

      await createPreIFixDeployApi(body);
      closeModal();
    },
  });
}
</script>

<style scoped></style>
