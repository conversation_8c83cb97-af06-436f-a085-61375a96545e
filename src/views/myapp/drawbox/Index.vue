<template>
  <div>
    <page-container title="DrawBox下载">
      <template #content>
        <a-tabs v-model:activeKey="activeKey" style="padding: 15px" destroy-inactive-tab-pane>
          <a-tab-pane key="1">
            <a-form
              ref="DrawBoxDownloadRef"
              :model="formStateDrawBoxDownload"
              :label-col="{ span: 1 }"
              :wrapper-col="{ span: 2 }"
              @finish="postDrawBoxDownload"
            >
              <a-form-item label="区服">
                <a-select
                  v-model:value="formStateDrawBoxDownload.region"
                  show-search
                  :loading="loading"
                  :options="regionOptions"
                ></a-select>
              </a-form-item>
              <a-form-item >
                <a-button type="primary" style="float: right" html-type="submit" :loading="loading">
                  拉取
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </template>
    </page-container>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { postCollectDrawBoxDownloadApi } from '@/api/sfmanage';
import { type SelectProps } from 'ant-design-vue';
import { getUserRegionApi } from '@/api/permission';
import type { PostDrawBoxDownloadReq } from '@/api/model';

const activeKey = ref<string>('1');
const loading = ref<boolean>(true);

const regionOptions = ref<SelectProps['options']>([]);
function getAllRegionName() {
  getUserRegionApi().then(data => {
    data.items.forEach((r: string) => {
      regionOptions.value.push({ value: r, label: r });
    });
  });
  // console.log('regionOptions: ', regionOptions);
}

onMounted(async () => {
  loading.value = true;
  await getAllRegionName();
  loading.value = false;
});

interface FormStateDrawBoxDownload {
  region: string;
}

const formStateDrawBoxDownload = reactive<FormStateDrawBoxDownload>({
  region: '',
});

async function postDrawBoxDownload() {
  loading.value = true;
  const body: PostDrawBoxDownloadReq = {
    region: formStateDrawBoxDownload.region,
  };
  await postCollectDrawBoxDownloadApi(body);
  loading.value = false;
}
</script>

<!-- border-radius 第一个值为左上角，第二个值为右上角，第三个值为右下角，第四个值为左下角。-->
<style scoped></style>
