<template>
  <div>
    <page-container title="InnerDev资源">
      <template #content>
        <a-tabs v-model:activeKey="activeKey" style="padding: 15px" destroy-inactive-tab-pane>
          <a-tab-pane key="1" tab="客户端资源">
            <a-form
              ref="GameResFormRef"
              :model="formStateGameRes"
              :label-col="{ span: 1 }"
              :wrapper-col="{ span: 2 }"
              @finish="postInnerDevGameRes"
            >
              <a-form-item label="区服">
                <a-select
                  v-model:value="formStateGameRes.region"
                  show-search
                  :loading="loading"
                  :options="regionOptions"
                ></a-select>
              </a-form-item>
              <a-form-item label="分支">
                <a-select
                  v-model:value="formStateGameRes.branch"
                  show-search
                  :loading="loading"
                  :options="branchOptions"
                ></a-select>
              </a-form-item>
              <a-form-item label="InnerDev后缀">
                <a-input v-model:value="formStateGameRes.gameResInfo.suffix"></a-input>
              </a-form-item>
              <a-form-item label="客户端类型">
                <a-select
                  v-model:value="formStateGameRes.gameResInfo.platform"
                  show-search
                  :loading="loading"
                  :options="platformOptions"
                ></a-select>
              </a-form-item>
              <a-form-item >
                <a-button type="primary" style="float: right" html-type="submit" :loading="loading">
                  发布
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>

          <a-tab-pane key="2" tab="客户端数值">
            <a-form
              ref="ClientDataFormRef"
              :model="formStateClientData"
              :label-col="{ span: 1 }"
              :wrapper-col="{ span: 2 }"
              @finish="postInnerDevClientData"
            >
              <a-form-item label="区服">
                <a-select
                  v-model:value="formStateClientData.region"
                  show-search
                  :loading="loading"
                  :options="regionOptions"
                ></a-select>
              </a-form-item>
              <a-form-item label="分支">
                <a-select
                  v-model:value="formStateClientData.branch"
                  show-search
                  :loading="loading"
                  :options="branchOptions"
                ></a-select>
              </a-form-item>
              <a-form-item label="InnerDev后缀">
                <a-input v-model:value="formStateClientData.suffix"
                ></a-input>
              </a-form-item>
              <a-form-item>
                <a-button type="primary" style="float: right" html-type="submit" :loading="loading">
                  发布
                </a-button>
              </a-form-item>
            </a-form>
          </a-tab-pane>
        </a-tabs>
      </template>
    </page-container>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import {
  getEnumConfigsApi,
  postInnerDevGameResApi,
  postInnerDevClientDataApi, getBranchVersionApi,
} from '@/api/sfmanage';
import { message, type SelectProps } from 'ant-design-vue';
import { getUserRegionApi } from '@/api/permission';
import type { PostInnerDevClientDataReq, PostInnerDevGameResReq } from '@/api/model';

const CategPlatform = 'client_version_oss_path_platform';
const activeKey = ref<string>('1');
const loading = ref<boolean>(true);

let platformOptions = ref<SelectProps['options']>([]);
async function getAllPlatform() {
  const respEnumEnv = await getEnumConfigsApi(CategPlatform);
  platformOptions.value = respEnumEnv.items.map(item => {
    return {
      label: item.label,
      value: item.value,
    };
  });
}

const regionOptions = ref<SelectProps['options']>([]);
function getAllRegionName() {
  getUserRegionApi().then(data => {
    data.items.forEach((r: string) => {
      regionOptions.value.push({ value: r, label: r });
    });
  });
  // console.log('regionOptions: ', regionOptions);
}

const branchOptions = ref<SelectProps['options']>([]);
const getAllBranch = async () => {
  const respBranch = await getBranchVersionApi({ type: 'data' });
  console.log(respBranch);
  branchOptions.value = Object.keys(respBranch).map(item => {
    return {
      label: item,
      value: item,
    };
  });
};

onMounted(async () => {
  loading.value = true;
  await getAllPlatform();
  await getAllRegionName();
  await getAllBranch();
  loading.value = false;
});

interface GameResInfo {
  platform: string;
  suffix: string;
}

interface FormStateGameRes {
  gameResInfo: GameResInfo;
  branch: string;
  region: string;
}

const formStateGameRes = reactive<FormStateGameRes>({
  gameResInfo: {
    suffix: '',
    platform: '',
  },
  branch: '',
  region: '',
});

interface FormStateClientData {
  suffix: string;
  branch: string;
  region: string;
}

const formStateClientData = reactive<FormStateClientData>({
  suffix: '',
  branch: '',
  region: '',
});

watch(
  () => formStateGameRes.region,
  (newVal, oldVal) => {
    formStateClientData.region = newVal;
  },
);

watch(
  () => formStateClientData.region,
  (newVal, oldVal) => {
    formStateGameRes.region = newVal;
  },
);
watch(
  () => formStateGameRes.branch,
  (newVal, oldVal) => {
    formStateClientData.branch = newVal;
  },
);

watch(
  () => formStateClientData.branch,
  (newVal, oldVal) => {
    formStateGameRes.branch = newVal;
  },
);

async function postInnerDevGameRes() {
  if (!/^\d+\.\d+\.\d+\.\d+$/.test(formStateGameRes.gameResInfo.suffix)) {
    message.error('版本号格式为a.b.c,d（a/b/c/d为纯数字）');
    return;
  }
  loading.value = true;
  const body: PostInnerDevGameResReq = {
    branch: formStateGameRes.branch,
    region: formStateGameRes.region,
    suffix: formStateGameRes.gameResInfo.suffix,
    platform: formStateGameRes.gameResInfo.platform,
  };
  await postInnerDevGameResApi(body);
  loading.value = false;
}

async function postInnerDevClientData() {
  if (!/^\d+\.\d+\.\d+\.\d+$/.test(formStateClientData.suffix)) {
    message.error('版本号格式为a.b.c,d（a/b/c/d为纯数字）');
    return;
  }
  loading.value = true;
  const body: PostInnerDevClientDataReq = {
    branch: formStateClientData.branch,
    region: formStateClientData.region,
    suffix: formStateClientData.suffix,
  };
  try {
    console.log("-----------------------------test be4 postInnerDevClientDataApi")
    await postInnerDevClientDataApi(body);
    console.log("-----------------------------test after postInnerDevClientDataApi")
  } finally {
    loading.value = false;
  }
}
</script>

<!-- border-radius 第一个值为左上角，第二个值为右上角，第三个值为右下角，第四个值为左下角。-->
<style scoped></style>
