<template>
  <div>
    <a-form layout="inline" :model="searchForm" @finish="searchInfo">
      <a-form-item
        label="环境"
        name="dispatch_url"
        :rules="[{ required: true, message: '请选择环境' }]"
      >
        <enum-selection
          v-model:value="searchForm.dispatch_url"
          category="global_dp_url"
          style="width: 120px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="渠道"
        name="channel_id"
        :rules="[{ required: true, message: '请选择渠道' }]"
      >
        <enum-selection
          v-model:value="searchForm.channel_id"
          category="channel_id"
          style="width: 150px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="子渠道"
        name="sub_channel_id"
        :rules="[{ required: true, message: '请选择子渠道' }]"
      >
        <enum-selection
          v-model:value="searchForm.sub_channel_id"
          category="sub_channel_id"
          style="width: 150px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="客户端类型"
        name="client_type"
        :rules="[{ required: true, message: '请选择客户端类型' }]"
      >
        <enum-selection
          v-model:value="searchForm.client_type"
          category="client_type"
          style="width: 200px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="客户端版本"
        name="client_version"
        :rules="[{ required: true, message: '请输入客户端版本' }]"
      >
        <a-input v-model:value="searchForm.client_version" placeholder="CNInnerXXXXX"></a-input>
      </a-form-item>
      <a-button type="primary" html-type="submit" :loading="loading">查询</a-button>
    </a-form>
    <div class="custom-antd-table-wrapper">
      <a-table
        style="margin-top: 10px"
        :data-source="datasource"
        :columns="columns"
        :loading="loading"
      >
        <template
          #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
        >
          <div style="padding: 8px">
            <a-input
              ref="searchInput"
              :placeholder="`Search ${column.dataIndex}`"
              :value="selectedKeys[0]"
              style="width: 188px; margin-bottom: 8px; display: block"
              @change="(e:any) => setSelectedKeys(e.target.value ? [e.target.value] : [])"
              @pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
            />
            <a-button
              type="primary"
              size="small"
              style="width: 90px; margin-right: 8px"
              @click="handleSearch(selectedKeys, confirm, column.dataIndex)"
            >
              <template #icon>
                <SearchOutlined />
              </template>
              Search
            </a-button>
            <a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
              Reset
            </a-button>
          </div>
        </template>
        <template #customFilterIcon="{ filtered }">
          <search-outlined :style="{ color: filtered ? '#108ee9' : undefined }" />
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { SearchOutlined } from '@ant-design/icons-vue';
import EnumSelection from '@/views/components/enum_selection.vue';
import dayjs from 'dayjs';
import {
  EnumConfigData,
  GlobalDispatchInfoRegion,
  GlobalDispatchInfoSearchParams,
} from '@/api/model';
import { getEnumConfigsApi, getGlobalDispatchApi } from '@/api/sfmanage';

const searchForm = ref<GlobalDispatchInfoSearchParams>({
  channel_id: '',
  client_type: '',
  client_version: '',
  sub_channel_id: '',
  dispatch_url: '',
});

const datasource = ref<GlobalDispatchInfoRegion[]>([]);
const columns = [
  {
    title: '区服名称',
    dataIndex: 'name',
    width: '200px',
    key: 'name',
    customFilterDropdown: true,
    onFilter: (value: any, record: any) =>
      record.name.toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownOpenChange: (visible: boolean) => {
      if (visible) {
        setTimeout(() => {
          searchInput.value.focus();
        }, 100);
      }
    },
  },
  {
    title: '区服对外展示名称',
    dataIndex: 'title',
  },
  {
    title: '区服地域枚举值',
    dataIndex: 'area',
    customRender: ({ text }: { text: number }) => {
      return areaOptions.value.find(item => item.value === `${text}`)?.label ?? '未知';
    },
  },
  {
    title: '业务',
    dataIndex: 'biz',
  },
  {
    title: 'sdk环境枚举值',
    dataIndex: 'env',
    customRender: ({ text }: { text: number }) => {
      return sdkOptions.value.find(item => item.value === `${text}`)?.label ?? '未知';
    },
  },
  {
    title: '是否推荐',
    dataIndex: 'is_recommend',
    customRender: ({ text }: { text: boolean }) => {
      return text ? '是' : '否';
    },
  },
  {
    title: '二级dp地址',
    dataIndex: 'dispatch_url',
  },
  {
    title: 'ping server地址',
    dataIndex: 'ping_url',
  },
  {
    title: '区服返回码',
    dataIndex: 'retcode',
  },
  {
    title: '提示信息',
    dataIndex: 'msg',
    customRender: ({ text, record }: { text: string; record: any }) => {
      if (record.stop_begin_time && record.stop_end_time) {
        return `${text}: 停服开始时间: ${dayjs
          .unix(record.stop_begin_time)
          .format('YYYY-MM-DD HH:mm:ss')}, 停服结束时间: ${dayjs
          .unix(record.stop_end_time)
          .format('YYYY-MM-DD HH:mm:ss')}`;
      }
      return text ? text : '-';
    },
  },
];

const state = reactive({
  searchText: '',
  searchedColumn: '',
});
const searchInput = ref();

const handleSearch = (selectedKeys: any, confirm: any, dataIndex: any) => {
  confirm();
  state.searchText = selectedKeys[0];
  state.searchedColumn = dataIndex;
};
const handleReset = (clearFilters: any) => {
  clearFilters({ confirm: true });
  state.searchText = '';
};

const loading = ref<boolean>(false);

const searchInfo = async () => {
  if (!searchForm.value) {
    return;
  }
  loading.value = true;
  await getGlobalDispatchApi(searchForm.value).then(data => {
    datasource.value = data.region_list;
  });
  loading.value = false;
};

const areaOptions = ref<EnumConfigData[]>([]);

const getAreaOptions = async () => {
  await getEnumConfigsApi('area').then(data => {
    areaOptions.value = data.items;
  });
};

const sdkOptions = ref<EnumConfigData[]>([]);

const getSdkOptions = async () => {
  await getEnumConfigsApi('sdk_env').then(data => {
    sdkOptions.value = data.items;
  });
};

onMounted(async () => {
  await getAreaOptions();
  await getSdkOptions();
});
</script>
<style scoped lang="less">
.custom-antd-table-wrapper {
  ::v-deep(.ant-table-container) {
    height: calc(80vh - 250px);
    overflow-y: auto;
  }
}
.highlight {
  background-color: rgb(255, 192, 105);
  padding: 0px;
}
</style>
