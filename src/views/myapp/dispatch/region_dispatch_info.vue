<template>
  <div>
    <a-form layout="inline" :model="searchForm" @finish="searchInfo">
      <a-form-item
        label="环境"
        name="dispatch_url"
        :rules="[{ required: true, message: '请选择环境' }]"
      >
        <enum-selection
          v-model:value="searchForm.dispatch_url"
          category="global_dp_url"
          style="width: 120px"
        ></enum-selection>
      </a-form-item>
      <a-form-item label="区服" name="region" :rules="[{ required: true, message: '请选择区服' }]">
        <region-selection v-model:value="searchForm.region" style="width: 150px"></region-selection>
      </a-form-item>
      <a-form-item
        label="非对称秘钥版本"
        name="rsa_ver"
        :rules="[{ required: true, message: '请选择非对称秘钥版本' }]"
      >
        <enum-selection
          v-model:value="searchForm.rsa_ver"
          category="rsa_ver"
          style="width: 50px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="渠道"
        name="channel_id"
        :rules="[{ required: true, message: '请选择渠道' }]"
      >
        <enum-selection
          v-model:value="searchForm.channel_id"
          category="channel_id"
          style="width: 150px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="子渠道"
        name="sub_channel_id"
        :rules="[{ required: true, message: '请选择非对称秘钥版本' }]"
      >
        <enum-selection
          v-model:value="searchForm.sub_channel_id"
          category="sub_channel_id"
          style="width: 150px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="客户端类型"
        name="client_type"
        :rules="[{ required: true, message: '请选择非对称客户端类型' }]"
      >
        <enum-selection
          v-model:value="searchForm.client_type"
          category="client_type"
          style="width: 200px"
        ></enum-selection>
      </a-form-item>
      <a-form-item
        label="客户端版本"
        name="client_version"
        :rules="[{ required: true, message: '请输入客户端版本' }]"
      >
        <a-input v-model:value="searchForm.client_version" placeholder="CNInnerXXXXX"></a-input>
      </a-form-item>
      <a-button type="primary" html-type="submit" :loading="loading">查询</a-button>
    </a-form>
    <a-empty
      v-if="!dispatchInfo?.region_name"
      :image="simpleImage"
      style="padding-top: 15%; height: calc(80vh - 200px)"
    />
    <a-descriptions
      v-else
      bordered
      style="margin-top: 25px; height: calc(80vh - 200px); overflow-y: auto"
      :label-style="{ width: '300px' }"
    >
      <a-descriptions-item label="区服名称">{{ dispatchInfo?.region_name }}</a-descriptions-item>
      <a-descriptions-item label="区服对外展示名">{{ dispatchInfo?.title }}</a-descriptions-item>
      <a-descriptions-item label="cdn探测用链接">
        {{ dispatchInfo?.cdn_check_url }}
      </a-descriptions-item>
      <a-descriptions-item label="sdk环境枚举值">
        {{ sdkOptions.find(item => item.value === `${dispatchInfo?.env}`)?.label ?? '未知' }}
      </a-descriptions-item>
      <a-descriptions-item label="gateway ip">
        {{ dispatchInfo?.gateway.ip }}
      </a-descriptions-item>
      <a-descriptions-item label="gateway 端口">
        {{ dispatchInfo?.gateway.port }}
      </a-descriptions-item>
      <!-- TODO: ipv6字段待服务器支持后补充 -->
      <a-descriptions-item label="区服自定义内容" :span="3">
        <template v-if="dispatchInfo?.region_ext">
          <pre>{{ JSON.stringify(dispatchInfo?.region_ext, null, 2) }}</pre>
        </template>
        <template v-else>暂无</template>
      </a-descriptions-item>
      <a-descriptions-item label="客户端数值" :span="3">
        <template v-if="dispatchInfo?.cdn_conf_ext?.design_data">
          <pre>{{ JSON.stringify(dispatchInfo?.cdn_conf_ext.design_data, null, 2) }}</pre>
        </template>
        <template v-else>暂无</template>
      </a-descriptions-item>
      <a-descriptions-item label="客户端资源" :span="3">
        <template v-if="dispatchInfo?.cdn_conf_ext?.game_res">
          <pre>{{ JSON.stringify(dispatchInfo?.cdn_conf_ext.game_res, null, 2) }}</pre>
        </template>
        <template v-else>暂无</template>
      </a-descriptions-item>
      <a-descriptions-item label="客户端静默数值" :span="3">
        <template v-if="dispatchInfo?.cdn_conf_ext?.silence_data">
          <pre>{{ JSON.stringify(dispatchInfo?.cdn_conf_ext.silence_data, null, 2) }}</pre>
        </template>
        <template v-else>暂无</template>
      </a-descriptions-item>
      <a-descriptions-item label="客户端首包加密数据" :span="3">
        {{ dispatchInfo?.client_secret_key }}
      </a-descriptions-item>
      <a-descriptions-item label="区服返回值">{{ dispatchInfo?.retcode }}</a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script setup lang="ts">
import EnumSelection from '@/views/components/enum_selection.vue';
import RegionSelection from '@/views/components/all_region_selection.vue';
import { EnumConfigData, RegionDispatchInfoRes, RegionDispatchInfoSearchParams } from '@/api/model';
import { getEnumConfigsApi, getRegionDispatchApi } from '@/api/sfmanage';
import { Empty } from 'ant-design-vue';
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const searchForm = ref<RegionDispatchInfoSearchParams>({
  channel_id: '',
  client_type: '',
  client_version: '',
  sub_channel_id: '',
  dispatch_url: '',
  rsa_ver: '',
  region: undefined,
});

const dispatchInfo = ref<RegionDispatchInfoRes>();

const loading = ref<boolean>(false);

const searchInfo = async () => {
  if (!searchForm.value) {
    return;
  }
  loading.value = true;
  await getRegionDispatchApi(searchForm.value).then(data => {
    dispatchInfo.value = data;
  });
  loading.value = false;
};

const sdkOptions = ref<EnumConfigData[]>([]);

const getSdkOptions = async () => {
  await getEnumConfigsApi('sdk_env').then(data => {
    sdkOptions.value = data.items;
  });
};

onMounted(async () => {
  await getSdkOptions();
});
</script>
<style scoped>
pre {
  white-space: pre-wrap; /* 保留空格和换行符，同时允许自动换行 */
  word-wrap: break-word; /* 允许长单词换行 */
}
</style>
