<template>
  <page-container :title="route.meta.title">
    <template #content>
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="一级DP">
          <global-dispatch-info></global-dispatch-info>
        </a-tab-pane>
        <a-tab-pane key="2" tab="二级DP">
          <region-dispatch-info></region-dispatch-info>
        </a-tab-pane>
      </a-tabs>
    </template>
  </page-container>
</template>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import GlobalDispatchInfo from '@/views/myapp/dispatch/global_dispatch_info.vue';
import RegionDispatchInfo from '@/views/myapp/dispatch/region_dispatch_info.vue';

const activeKey = ref('1');
const route = useRoute(); // 获取当前路由
</script>
