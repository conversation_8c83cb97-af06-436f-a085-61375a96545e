<template>
  <page-container :title="`${route.meta.title} `">
    <!-- Access the state directly from the stores -->
    <!--    <div>Current Count: {{ counter.count }}</div>-->
    <iframe
      src="https://sre-kibana.juequling.com/app/dashboards#/view/6eb4d7e0-f00d-11ed-aa6e-01eb09e7ccc8?embed=true&_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-7d%2Fd,to:now))&_a=(description:'',filters:!(),fullScreenMode:!f,options:(hidePanelTitles:!f,syncColors:!f,useMargins:!t),panels:!((embeddableConfig:(attributes:(references:!((id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-current-indexpattern,type:index-pattern),(id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-layer-c70823b5-513b-4a53-8496-a24582bcf59c,type:index-pattern)),state:(datasourceStates:(indexpattern:(layers:(c70823b5-513b-4a53-8496-a24582bcf59c:(columnOrder:!(a9ff1713-2a46-4626-a13b-dae0003b7788),columns:(a9ff1713-2a46-4626-a13b-dae0003b7788:(customLabel:!t,dataType:number,isBucketed:!f,label:%E4%BB%BB%E5%8A%A1%E6%89%A7%E8%A1%8C%E6%AC%A1%E6%95%B0,operationType:unique_count,scale:ratio,sourceField:task_id.keyword)),incompleteColumns:())))),filters:!(),query:(language:kuery,query:''),visualization:(accessor:a9ff1713-2a46-4626-a13b-dae0003b7788,layerId:c70823b5-513b-4a53-8496-a24582bcf59c)),title:'',type:lens,visualizationType:lnsMetric),enhancements:(),hidePanelTitles:!f),gridData:(h:15,i:'11f35e7d-3dad-488f-b0ed-fb3a7092d2c7',w:24,x:0,y:0),panelIndex:'11f35e7d-3dad-488f-b0ed-fb3a7092d2c7',title:%E4%BB%BB%E5%8A%A1%E6%80%BB%E6%89%A7%E8%A1%8C%E6%AC%A1%E6%95%B0,type:lens,version:'7.14.2'),(embeddableConfig:(attributes:(references:!((id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-current-indexpattern,type:index-pattern),(id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-layer-4f18b8d4-e78d-4a09-a3d4-65e80375cab4,type:index-pattern)),state:(datasourceStates:(indexpattern:(layers:('4f18b8d4-e78d-4a09-a3d4-65e80375cab4':(columnOrder:!('0a44016f-3c43-45e3-a505-005716dbb8a8',d6b99810-1ddd-434d-9659-552b1172962e,d6b99810-1ddd-434d-9659-552b1172962eX0),columns:('0a44016f-3c43-45e3-a505-005716dbb8a8':(customLabel:!t,dataType:string,isBucketed:!t,label:%E4%BB%BB%E5%8A%A1%E6%88%90%E5%8A%9F%E5%A4%B1%E8%B4%A5%E6%AF%94%E4%BE%8B,operationType:terms,params:(missingBucket:!f,orderBy:(fallback:!t,type:alphabetical),orderDirection:asc,otherBucket:!t,size:5),scale:ordinal,sourceField:status.keyword),d6b99810-1ddd-434d-9659-552b1172962e:(dataType:number,isBucketed:!f,label:'count()',operationType:formula,params:(formula:'count()',isFormulaBroken:!f),references:!(d6b99810-1ddd-434d-9659-552b1172962eX0),scale:ratio),d6b99810-1ddd-434d-9659-552b1172962eX0:(customLabel:!t,dataType:number,isBucketed:!f,label:'Part%20of%20count()',operationType:count,scale:ratio,sourceField:Records)),incompleteColumns:())))),filters:!(),query:(language:kuery,query:''),visualization:(layers:!((categoryDisplay:default,groups:!('0a44016f-3c43-45e3-a505-005716dbb8a8'),layerId:'4f18b8d4-e78d-4a09-a3d4-65e80375cab4',legendDisplay:default,metric:d6b99810-1ddd-434d-9659-552b1172962e,nestedLegend:!f,numberDisplay:percent)),palette:(name:default,type:palette),shape:pie)),title:'',type:lens,visualizationType:lnsPie),enhancements:(),hidePanelTitles:!f),gridData:(h:15,i:d33f8371-2a4c-4451-9887-4e3f7f0e3253,w:24,x:24,y:0),panelIndex:d33f8371-2a4c-4451-9887-4e3f7f0e3253,title:%E4%BB%BB%E5%8A%A1%E6%88%90%E5%8A%9F%E5%A4%B1%E8%B4%A5%E7%99%BE%E5%88%86%E6%AF%94,type:lens,version:'7.14.2'),(embeddableConfig:(attributes:(references:!((id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-current-indexpattern,type:index-pattern),(id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-layer-03ba6d12-f672-407d-9312-19aa60d5cba0,type:index-pattern)),state:(datasourceStates:(indexpattern:(layers:('03ba6d12-f672-407d-9312-19aa60d5cba0':(columnOrder:!('15eb4791-7830-43fd-a0e0-06d1013569c5',eb53a6e5-fc32-4b8d-8015-fd0f02254547),columns:('15eb4791-7830-43fd-a0e0-06d1013569c5':(dataType:string,isBucketed:!t,label:'Top%20values%20of%20task_name.keyword',operationType:terms,params:(missingBucket:!f,orderBy:(columnId:eb53a6e5-fc32-4b8d-8015-fd0f02254547,type:column),orderDirection:desc,otherBucket:!t,size:10),scale:ordinal,sourceField:task_name.keyword),eb53a6e5-fc32-4b8d-8015-fd0f02254547:(dataType:number,isBucketed:!f,label:'Count%20of%20records',operationType:count,scale:ratio,sourceField:Records)),incompleteColumns:())))),filters:!(),query:(language:kuery,query:''),visualization:(layers:!((categoryDisplay:default,groups:!('15eb4791-7830-43fd-a0e0-06d1013569c5'),layerId:'03ba6d12-f672-407d-9312-19aa60d5cba0',legendDisplay:default,metric:eb53a6e5-fc32-4b8d-8015-fd0f02254547,nestedLegend:!f,numberDisplay:percent)),shape:donut)),title:'',type:lens,visualizationType:lnsPie),enhancements:(),hidePanelTitles:!f),gridData:(h:24,i:'05676a9b-58d3-4a1a-ad76-a6fe87a90b19',w:19,x:0,y:15),panelIndex:'05676a9b-58d3-4a1a-ad76-a6fe87a90b19',title:%E7%A7%81%E6%9C%8D%E5%90%84%E7%B1%BB%E6%93%8D%E4%BD%9C%E7%99%BE%E5%88%86%E6%AF%94,type:lens,version:'7.14.2'),(embeddableConfig:(attributes:(references:!((id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-current-indexpattern,type:index-pattern),(id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-layer-c63ba8a0-0762-4ecd-947e-e17288b1c71e,type:index-pattern)),state:(datasourceStates:(indexpattern:(layers:(c63ba8a0-0762-4ecd-947e-e17288b1c71e:(columnOrder:!('5b1239c1-fa09-474d-bd81-c15c8dd764c8','1cbee989-dafe-47ff-9d74-ae5b430485d3'),columns:('1cbee989-dafe-47ff-9d74-ae5b430485d3':(dataType:number,isBucketed:!f,label:'Count%20of%20records',operationType:count,scale:ratio,sourceField:Records),'5b1239c1-fa09-474d-bd81-c15c8dd764c8':(dataType:string,isBucketed:!t,label:'Top%20values%20of%20region_name.keyword',operationType:terms,params:(missingBucket:!f,orderBy:(columnId:'1cbee989-dafe-47ff-9d74-ae5b430485d3',type:column),orderDirection:desc,otherBucket:!t,size:20),scale:ordinal,sourceField:region_name.keyword)),incompleteColumns:())))),filters:!(),query:(language:kuery,query:''),visualization:(axisTitlesVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),fittingFunction:None,gridlinesVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),layers:!((accessors:!('1cbee989-dafe-47ff-9d74-ae5b430485d3'),layerId:c63ba8a0-0762-4ecd-947e-e17288b1c71e,position:top,seriesType:bar_horizontal,showGridlines:!f,xAccessor:'5b1239c1-fa09-474d-bd81-c15c8dd764c8')),legend:(isVisible:!t,position:right),preferredSeriesType:bar_horizontal,tickLabelsVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),valueLabels:hide,yLeftExtent:(mode:full),yRightExtent:(mode:full))),title:'',type:lens,visualizationType:lnsXY),enhancements:(),hidePanelTitles:!f),gridData:(h:24,i:b79edbbd-00f7-4ce3-a305-839152526afb,w:29,x:19,y:15),panelIndex:b79edbbd-00f7-4ce3-a305-839152526afb,title:%E7%A7%81%E6%9C%8D%E6%93%8D%E4%BD%9C%E6%AC%A1%E6%95%B0,type:lens,version:'7.14.2'),(embeddableConfig:(attributes:(references:!((id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-current-indexpattern,type:index-pattern),(id:b38a2790-f002-11ed-aa6e-01eb09e7ccc8,name:indexpattern-datasource-layer-f2d8bc89-5094-4162-8b44-b77a39fc90ee,type:index-pattern)),state:(datasourceStates:(indexpattern:(layers:(f2d8bc89-5094-4162-8b44-b77a39fc90ee:(columnOrder:!('33072a0e-21c8-458b-ac8b-c29042525c52',f845f38e-1ce4-4314-8e33-86d7710cedc0),columns:('33072a0e-21c8-458b-ac8b-c29042525c52':(dataType:date,isBucketed:!t,label:'@timestamp',operationType:date_histogram,params:(interval:'1d'),scale:interval,sourceField:'@timestamp'),f845f38e-1ce4-4314-8e33-86d7710cedc0:(customLabel:!t,dataType:number,isBucketed:!f,label:%E6%8C%89%E6%97%B6%E9%97%B4%E7%BB%9F%E8%AE%A1%E6%93%8D%E4%BD%9C%E6%AC%A1%E6%95%B0,operationType:count,scale:ratio,sourceField:Records)),incompleteColumns:())))),filters:!(),query:(language:kuery,query:''),visualization:(axisTitlesVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),fittingFunction:None,gridlinesVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),layers:!((accessors:!(f845f38e-1ce4-4314-8e33-86d7710cedc0),layerId:f2d8bc89-5094-4162-8b44-b77a39fc90ee,position:top,seriesType:line,showGridlines:!f,xAccessor:'33072a0e-21c8-458b-ac8b-c29042525c52')),legend:(isVisible:!t,position:right),preferredSeriesType:line,tickLabelsVisibilitySettings:(x:!t,yLeft:!t,yRight:!t),valueLabels:hide,yLeftExtent:(mode:full),yRightExtent:(mode:full))),title:'',type:lens,visualizationType:lnsXY),enhancements:(),hidePanelTitles:!f),gridData:(h:17,i:'8afc610a-88b2-4ef4-8b09-09f29d14df18',w:48,x:0,y:39),panelIndex:'8afc610a-88b2-4ef4-8b09-09f29d14df18',title:%E6%8C%89%E7%85%A7%E6%97%B6%E9%97%B4%E7%BB%9F%E8%AE%A1%E6%93%8D%E4%BD%9C%E6%AC%A1%E6%95%B0,type:lens,version:'7.14.2')),query:(language:kuery,query:''),tags:!(),timeRestore:!f,title:%E7%A7%81%E6%9C%8D%E6%95%B0%E6%8D%AE%E6%A6%82%E8%A7%88,viewMode:view)"
      height="1700px"
      width="100%"
      border="0px"
      frameborder="no"
      scrolling="no"
      allowtransparency="yes"
    ></iframe>
  </page-container>
</template>

<script setup>
import { useCounterStore } from '@/stores/modules/counter';
import { useRoute } from 'vue-router';

const route = useRoute();

const counter = useCounterStore();
counter.count++;
// with autocompletion ✨;
counter.$patch({ count: counter.count + 1 });
// or using an action instead
counter.increment();
</script>
