<template>
  <PageContainer>
    <div class="background-container">
      <iframe style="width: 100%; height: 100%" src="https://ai-chat.mihoyo.com/"></iframe>
    </div>
  </PageContainer>
</template>

<script lang="ts" setup>
import { PageContainer as PageContainer } from '@ant-design-vue/pro-layout';
import { SmileTwoTone } from '@ant-design/icons-vue';
</script>
<style scoped>
.background-container {
  width: 100%;
  height: 90vh;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}
</style>
