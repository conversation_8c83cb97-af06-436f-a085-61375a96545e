<template>
  <div style="margin-right: 12px">
    <a-space>
      <a style="padding: 0 12px; display: inline-block; user-select: none" @click="handleClick">
        <BgColorsOutlined />
      </a>
      <a href="https://app.hoyowave.com/service-hub?id=77810113271606" target="_blank">
        <WechatOutlined />
        <a-button type="link" style="color: white">技术支持</a-button>
      </a>
      <a href="https://km.mihoyo.com/articleBase/1852/1430993" target="_blank">
        <QuestionCircleOutlined />
        <a-button type="link" style="color: white">帮助文档</a-button>
      </a>
      <span style="color: white; margin-right: 5px">欢迎回来，{{ currentUser }}</span>
      <span style="vertical-align: super">
        <a-dropdown>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <template #icon>
                  <SettingOutlined />
                </template>
                <span>个人设置</span>
              </a-menu-item>
              <a-menu-item @click="logout">
                <template #icon>
                  <LogoutOutlined />
                </template>
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
          <a-avatar shape="circle" :size="28">
            <template #icon>
              <img :src="avatar" alt="avatar image" />
            </template>
          </a-avatar>
        </a-dropdown>
      </span>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import {
  SettingOutlined,
  LogoutOutlined,
  BgColorsOutlined,
  WechatOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons-vue';
import { apply, randomTheme } from '../../hooks/useTheme';
import { ref } from 'vue';
import { useUserStore } from '@/stores/modules/user';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

// 整个路由的上下文
const router = useRouter();
// 表示当前路由上下文
const route = useRoute();

const userStore = useUserStore();
const currentUser = ref<string>(userStore.getUser.display_name);
const avatar = ref<string>(userStore.getUser.thumbnail);
const handleClick = () => {
  apply(randomTheme());
};

function logout() {
  userStore.loginOut();
  message.success('退出成功！欢迎再次回来');
  // 刷新页面
  location.reload();
}
</script>
