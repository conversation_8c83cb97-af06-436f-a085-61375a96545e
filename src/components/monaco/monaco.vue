<template>
  <div ref="editorContainer" className="monaco-editor" :style="{ width, height }"></div>
</template>

<script>
import { onMounted, onBeforeUnmount, ref } from 'vue';
import * as monaco from 'monaco-editor';
import EditorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
import <PERSON>sonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
import CssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
import HtmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
import TsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

self.MonacoEnvironment = {
  getWorker(_, label) {
    if (label === 'json') {
      return new JsonWorker();
    }
    if (label === 'css' || label === 'scss' || label === 'less') {
      return new CssWorker();
    }
    if (label === 'html' || label === 'handlebars' || label === 'razor') {
      return new HtmlWorker();
    }
    if (label === 'typescript' || label === 'javascript') {
      return new TsWorker();
    }
    return new EditorWorker();
  },
};
export default {
  name: 'MonacoEditor',
  props: {
    modelValue: {
      type: String,
      required: true,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    highlights: {
      type: Array,
      default: () => [''], // 使用一个函数返回一个新的数组实例
    },
    enableAutoScroll: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    const editorContainer = ref(null);
    let editor;

    // 定义自定义主题，确保 foreground 和 background 都有设置
    const defineCustomTheme = () => {
      // 定义自定义主题，用于高亮日志级别关键字
      monaco.editor.defineTheme('custom-log-theme', {
        base: 'vs-dark', // 基于已有的 dark 主题
        inherit: true, // 继承默认样式
        rules: [
          { token: '', foreground: 'ffffff' }, // 设置默认字体颜色
          { token: 'custom-error', foreground: 'ff0000', fontStyle: 'bold' },
          { token: 'custom-warning', foreground: 'ffa500', fontStyle: 'bold' },
          { token: 'custom-info', foreground: '00ff00' },
          { token: 'custom-debug', foreground: '0080ff' },
          { token: 'custom-highlight', foreground: 'ff0000', background: 'ffcccc' },
        ],
        colors: {
          // 'editor.foreground': 'black', // 必需的前景色配置
          // 'editor.background': '#1e1e1e', // 必需的背景色配置
        },
      });
    };

    const initEditor = () => {
      editor = monaco.editor.create(editorContainer.value, {
        value: parseLog(props.modelValue),
        theme: 'custom-log-theme', // 主题 vs-dark
        language: 'typescript',
        folding: true, // 是否折叠
        foldingHighlight: true, // 折叠等高线
        foldingStrategy: 'indentation', // 折叠方式  auto | indentation
        showFoldingControls: 'always', // 是否一直显示折叠 always | mouseover
        disableLayerHinting: true, // 等宽优化
        emptySelectionClipboard: false, // 空选择剪切板
        selectionClipboard: false, // 选择剪切板
        automaticLayout: true, // 自动布局
        codeLens: false, // 代码镜头
        scrollBeyondLastLine: false, // 滚动完最后一行后再滚动一屏幕
        scrollbar: {
          alwaysConsumeMouseWheel: false, // 鼠标滚轮是否总是滚动编辑器内容而不是滚动页面
          horizontal: 'auto', // 水平滚动条的可见性。可以设置为 'auto', 'visible' 或 'hidden'
          vertical: 'auto', // 垂直滚动条的可见性。可以设置为 'auto', 'visible' 或 'hidden'
        },
        colorDecorators: true, // 颜色装饰器
        accessibilitySupport: 'off', // 辅助功能支持  "auto" | "off" | "on"
        lineNumbers: 'on', // 行号 取值： "on" | "off" | "relative" | "interval" | function
        lineNumbersMinChars: 5, // 行号最小字符   number
        enableSplitViewResizing: false,
        readOnly: true, //是否只读  取值 true | false
        minimap: {
          enabled: false, // 不要小地图
        },
      });
      monaco.editor.setTheme('custom-log-theme'); // 确保设置主题

      // 监听内容变化并滚动到底部
      editor.onDidChangeModelContent(() => {
        scrollToBottom();
      });
    };
    onBeforeMount(() => {
      defineCustomTheme();
    });
    onMounted(() => {
      nextTick(() => {
        initEditor();
      });
      // 为什么要睡500ms? 为了防止monaco editor组件再渲染完成后，自定义的样式还没有完全加载
      // setTimeout(initEditor, 500);
      updateTokenProvider();
    });

    // 监听v-model 即内置变量 modelValue 的变化更新编辑器内容
    watch(
      () => props.modelValue,
      newValue => {
        editor.setValue(newValue);
      },
    );
    // 监听 highlights 的变化并重新设置 TokenProvider
    watch(
      () => props.highlights,
      () => {
        updateTokenProvider();
      },
      { deep: true }, // 深度监听数组变化
    );

    // 定义更新 TokenProvider 的方法
    const updateTokenProvider = () => {
      let rootRegex = [
        [/LOG_ERROR/, 'custom-error'],
        [/LOG_WARNING/, 'custom-warning'],
        [/LOG_INFO/, 'custom-info'],
        [/LOG_DEBUG/, 'custom-debug'],
      ];
      const highlights = Array.isArray(props.highlights) ? props.highlights : [];
      highlights.forEach(highlight => {
        if (highlight !== '') {
          rootRegex.push([new RegExp(highlight, 'g'), 'custom-highlight']);
        }
      });
      // 注册自定义的 `token` 规则以识别日志级别关键字
      monaco.languages.setMonarchTokensProvider('typescript', {
        tokenizer: {
          root: rootRegex,
        },
      });
    };

    // 自动滚动到内容底部的函数
    function scrollToBottom() {
      if (!props.enableAutoScroll) {
        return;
      }
      const editorModel = editor.getModel();
      if (editorModel) {
        const lineCount = editorModel.getLineCount();
        editor.revealLine(lineCount); // 滚动到最后一行
      }
    }

    function parseLog(log) {
      // 解析转义序列并应用样式
      // \u001b[0m\n\u001b[31mLOG_ERROR 2023-06-21 10:04:12.495 11422
      // eslint-disable-next-line no-control-regex
      // return log.replace(/\u001b/g, '');
      return log;
    }

    onBeforeUnmount(() => {
      editor.dispose();
    });

    return {
      editorContainer,
    };
  },
};
</script>

<style scoped>
.monaco-editor {
  width: 100%;
  height: 100%;
  border: 1px solid #ccc; /* 设置边框颜色为浅灰色 */
  border-radius: 4px; /* 设置边框圆角 */
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1); /* 设置阴影效果 */
}

.error-line {
  background-color: #ffeeee;
}

/* 确保编辑器容器在容器内填满 */
div[ref='editorContainer'] {
  display: block;
  width: 100%;
  height: 100%;
}
</style>
