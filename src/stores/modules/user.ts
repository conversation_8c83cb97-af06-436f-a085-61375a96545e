import { defineStore } from 'pinia';
import { store } from '@/stores';

import type { LoginReq, SSOLoginReq, UserInfoRes } from '@/api/user';
import { loginApi, getUserInfoApi, ssoLoginApi } from '@/api/user';
import { getMyPolicyApi } from '@/api/permission';
import type { MenuData } from '@ant-design-vue/pro-layout';
import type { RouteRecord } from 'vue-router';
import { getMenuApi } from '@/api/Menu';

const localStorage = window.localStorage;
const TOKEN_KEY = 'token';
const USER_KEY = 'user';
const USERNAME_KEY = 'username';
export const useUserStore = defineStore({
  id: 'userStore',
  state: () => {
    return {
      username: '',
      // user info
      userInfo: {},
      // token
      token: '',
      permissions: [],
      menuData: [],
      userMenus: [],
    };
  },
  getters: {
    getToken(): string | null {
      return this.token || localStorage.getItem(TOKEN_KEY);
    },
    getUSerName(): string {
      const username = localStorage.getItem(USERNAME_KEY)!;
      return username ? username : this.username;
    },
    getUser(): UserInfoRes {
      const user = localStorage.getItem(USER_KEY);
      if (user != null) {
        const userInfo = JSON.parse(user);
        return userInfo as UserInfoRes;
      } else {
        return this.userInfo as UserInfoRes;
      }
    },
    getDynamicRoute(): RouteRecord[] {
      return this.menuData ? this.menuData : localStorage.getItem('menuData');
    },
  },
  // 也可以这样定义
  // state: () => ({ count: 0 })
  actions: {
    setToken(info: string) {
      this.token = info;
      localStorage.setItem(TOKEN_KEY, info);
    },
    setUsername(info: string) {
      this.username = info;
      localStorage.setItem(USERNAME_KEY, info);
    },
    setDynamicRoute(menuData: RouteRecord[]) {
      this.menuData = menuData;
      localStorage.setItem('menuData', JSON.stringify(menuData));
    },
    /**
     * @description: login
     */
    async login(username: string, password: string) {
      localStorage.removeItem('token');
      const loginReq: LoginReq = { username: username, password: password };
      return await loginApi(loginReq).then(LoginRes => {
        const token = LoginRes.token;
        this.setToken(token);
        this.setUsername(username);
        //登录成后 获取用户详细信息
        const user = this.loginAfter();
        return user;
      });
    },
    async ssologin(ticket: string) {
      localStorage.removeItem('token');
      const loginReq: SSOLoginReq = { ticket: ticket };
      return await ssoLoginApi(loginReq)
        .then(LoginRes => {
          const token = LoginRes.token;
          this.setToken(token);
          //登录成后 获取用户详细信息
          const user = this.loginAfter();
          return user;
        })
        .catch(error => {
          return Promise.reject(error);
        });
    },
    async loginAfter() {
      return await getUserInfoApi().then(userInfo => {
        this.userInfo = userInfo;
        this.setUsername(userInfo.user_name);
        localStorage.setItem(USER_KEY, JSON.stringify(userInfo));
        return userInfo;
      });
    },
    // 清空state，并localStorage
    async loginOut() {
      this.userInfo = {};
      this.setToken('');
      localStorage.removeItem('token');
    },

    async getPermissions(perms_type: string) {
      return await getMyPolicyApi(perms_type).then(permissions => {
        // console.log(permissions);
        this.permissions = permissions;
        return permissions;
      });
    },

    async getUserMenu(category: string) {
      return await getMenuApi(category).then(menus => {
        // console.log(menus);
        this.userMenus = menus;
        return menus;
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
