import { store } from '@/stores';
import { defineStore } from 'pinia';
import type { RegionStatus } from '@/api/model';
import { getSfStatusApi } from '@/api/sfmanage';
import dayjs from 'dayjs';

export const useCurrentSFState = defineStore('sfState', {
  state: () => {
    return {
      currentSF: '',
      currentModule: '',
      currentSFStatus: {
        id: 1,
        region_name: '',
        status: 2,
        created_time: '',
        updated_time: '',
        Locked: false,
        locker: '',
        lock_duration: 0,
        mirror_config: '{}',
        describe: '',
        healthy_status: 1,
        user_count: 0,
      },
      count: 0,
      currentSFActions: {},
    };
  },
  // 也可以这样定义
  getters: {
    currentValue: state => state.currentSF || localStorage.getItem('currentSF'),
    getCurrentSFStatus(): RegionStatus {
      // console.log('this.currentSFStatus.region_name: ', this.currentSFStatus.region_name);
      console.log('this.currentSFStatus: ', this.currentSFStatus);
      console.log('localStorage: ', localStorage.getItem('currentSFStatus'));
      return JSON.parse(localStorage.getItem('currentSFStatus')) || this.currentSFStatus;
    },
    getCurrentSFActions(): string[] {
      return this.currentSFActions;
    },
  },
  actions: {
    setCurrentSF(val: string) {
      this.currentSF = val;
      localStorage.setItem('currentSF', val);
    },
    async setCurrentSFStatus(region_name: string) {
      await getSfStatusApi({ region: region_name }).then(data => {
        if (!data) {
          return;
        }
        // console.log('getSfStatusApi: ', data);
        // currentSFStatus.value = data;
        // this.currentSFStatus = data;
        // Object.assign(this.currentSFStatus, data);
        this.currentSFStatus = { ...data };
        this.currentSFStatus.created_time = dayjs(data.created_time).format('YYYY-MM-DD HH:mm:ss');
        this.currentSFStatus.updated_time = dayjs(data.updated_time).format('YYYY-MM-DD HH:mm:ss');

        localStorage.setItem('currentSFStatus', JSON.stringify(data));

        console.log('------------------this.currentSFStatus: ', this.currentSFStatus);
      });
    },
    setCurrentSFActions(val: string[]) {
      this.currentSFActions = val;
    },
    setCurrentSfLock(locked: boolean) {
      this.currentSFStatus.Locked = locked;
    },
    refresh() {
      this.count++;
    },
  },
});

// Need to be used outside the setup
export function useCurrentSFStateWithout() {
  return useCurrentSFState(store);
}
