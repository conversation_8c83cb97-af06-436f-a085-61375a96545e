FROM node:18 AS builder

COPY . /zzz-support-platform
WORKDIR /zzz-support-platform
RUN npm install  && npm install -g typescript vue-tsc
RUN chmod +x node_modules/.bin/*
RUN npm run build


FROM nginx:1.25.3

ENV APP_NAME="op-web-op-cloudman-takumi"
COPY --from=builder /zzz-support-platform/dist/ /usr/share/nginx/html/
COPY --from=builder /zzz-support-platform/default.conf /etc/nginx/conf.d/default.conf
CMD ["nginx", "-g", "daemon off;"]